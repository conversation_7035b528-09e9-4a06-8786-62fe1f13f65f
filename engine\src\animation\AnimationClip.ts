/**
 * 动画片段
 * 表示一段可播放的动画数据
 */
import * as THREE from 'three';

/**
 * 动画轨道类型
 */
export enum TrackType {
  /** 位置轨道 */
  POSITION = 'position',
  /** 旋转轨道 */
  ROTATION = 'rotation',
  /** 缩放轨道 */
  SCALE = 'scale',
  /** 变换矩阵轨道 */
  MATRIX = 'matrix',
  /** 颜色轨道 */
  COLOR = 'color',
  /** 透明度轨道 */
  OPACITY = 'opacity',
  /** 可见性轨道 */
  VISIBILITY = 'visibility',
  /** 权重轨道 */
  WEIGHT = 'weight',
  /** 变形目标轨道 */
  MORPH_TARGET = 'morphTarget',
  /** 自定义属性轨道 */
  CUSTOM = 'custom'
}

/**
 * 动画插值类型
 */
export enum InterpolationType {
  /** 线性插值 */
  LINEAR = 'linear',
  /** 阶梯插值 */
  STEP = 'step',
  /** 立方样条插值 */
  CUBIC = 'cubic',
  /** 贝塞尔曲线插值 */
  BEZIER = 'bezier'
}

/**
 * 动画循环模式
 */
export enum LoopMode {
  /** 不循环 */
  NONE = 'none',
  /** 重复循环 */
  REPEAT = 'repeat',
  /** 往复循环 */
  PING_PONG = 'pingpong'
}

/**
 * 关键帧接口
 */
export interface Keyframe {
  /** 时间点（秒） */
  time: number;
  /** 值 */
  value: any;
  /** 插值类型 */
  interpolation?: InterpolationType;
  /** 入切线（用于曲线插值） */
  inTangent?: any;
  /** 出切线（用于曲线插值） */
  outTangent?: any;
}

/**
 * 动画轨道接口
 */
export interface AnimationTrack {
  /** 轨道类型 */
  type: TrackType;
  /** 目标路径（如骨骼名称或属性路径） */
  targetPath: string;
  /** 关键帧数组 */
  keyframes: Keyframe[];
  /** 默认插值类型 */
  defaultInterpolation: InterpolationType;
}

/**
 * 动画片段选项
 */
export interface AnimationClipOptions {
  /** 动画名称 */
  name?: string;
  /** 动画轨道 */
  tracks?: AnimationTrack[];
  /** 动画持续时间（秒） */
  duration?: number;
  /** 循环模式 */
  loopMode?: LoopMode;
  /** 播放速度 */
  speed?: number;
  /** 是否启用 */
  enabled?: boolean;
  /** 权重 */
  weight?: number;
  /** 混合时间（秒） */
  blendTime?: number;
}

/**
 * 动画片段类
 */
export class AnimationClip {
  /** 动画名称 */
  public name: string;

  /** 动画轨道 */
  public tracks: AnimationTrack[];

  /** 动画持续时间（秒） */
  public duration: number;

  /** 循环模式 */
  public loopMode: LoopMode;

  /** 播放速度 */
  public speed: number;

  /** 是否启用 */
  public enabled: boolean;

  /** 权重 */
  public weight: number;

  /** 混合时间（秒） */
  public blendTime: number;

  /** Three.js动画片段（用于兼容） */
  private _threeClip: THREE.AnimationClip | null = null;

  /** 缓存的时间 */
  private _cachedTime: number = -1;

  /** 缓存的结果 */
  private _cachedResult: Map<string, any> | null = null;

  /** 是否启用缓存 */
  private _cacheEnabled: boolean = true;

  /**
   * 转换为Three.js动画片段
   * @returns Three.js动画片段
   */
  public toThreeAnimationClip(): THREE.AnimationClip {
    // 如果已经有缓存的Three.js动画片段，则直接返回
    if (this._threeClip) {
      return this._threeClip;
    }

    // 创建轨道数组
    const tracks: THREE.KeyframeTrack[] = [];

    // 转换每个轨道
    for (const track of this.tracks) {
      // 确定轨道类型和名称
      let trackType: typeof THREE.KeyframeTrack;
      const trackName = `${track.targetPath}.${this.getThreePropertyName(track.type)}`;

      switch (track.type) {
        case TrackType.POSITION:
        case TrackType.SCALE:
          trackType = THREE.VectorKeyframeTrack;
          break;
        case TrackType.ROTATION:
          trackType = THREE.QuaternionKeyframeTrack;
          break;
        case TrackType.COLOR:
          trackType = THREE.ColorKeyframeTrack;
          break;
        case TrackType.OPACITY:
        case TrackType.WEIGHT:
        case TrackType.MORPH_TARGET:
          trackType = THREE.NumberKeyframeTrack;
          break;
        case TrackType.VISIBILITY:
          trackType = THREE.BooleanKeyframeTrack;
          break;
        default:
          trackType = THREE.KeyframeTrack;
      }

      // 提取时间和值
      const times: number[] = [];
      const values: any[] = [];

      for (const keyframe of track.keyframes) {
        times.push(keyframe.time);

        // 根据轨道类型处理值
        if (track.type === TrackType.POSITION || track.type === TrackType.SCALE) {
          const vector = keyframe.value as THREE.Vector3;
          values.push(vector.x, vector.y, vector.z);
        } else if (track.type === TrackType.ROTATION) {
          const quaternion = keyframe.value as THREE.Quaternion;
          values.push(quaternion.x, quaternion.y, quaternion.z, quaternion.w);
        } else if (track.type === TrackType.COLOR) {
          const color = keyframe.value as THREE.Color;
          values.push(color.r, color.g, color.b);
        } else {
          // 其他类型直接添加值
          values.push(keyframe.value);
        }
      }

      // 创建Three.js关键帧轨道
      const threeTrack = new trackType(trackName, times, values.flat());
      tracks.push(threeTrack);
    }

    // 创建Three.js动画片段
    this._threeClip = new THREE.AnimationClip(this.name, this.duration, tracks);

    return this._threeClip;
  }

  /**
   * 获取Three.js属性名称
   * @param trackType 轨道类型
   * @returns Three.js属性名称
   */
  private getThreePropertyName(trackType: TrackType): string {
    switch (trackType) {
      case TrackType.POSITION:
        return 'position';
      case TrackType.ROTATION:
        return 'quaternion';
      case TrackType.SCALE:
        return 'scale';
      case TrackType.COLOR:
        return 'color';
      case TrackType.OPACITY:
        return 'opacity';
      case TrackType.VISIBILITY:
        return 'visible';
      case TrackType.WEIGHT:
        return 'weight';
      case TrackType.MORPH_TARGET:
        return 'morphTargetInfluences';
      default:
        return trackType;
    }
  }

  /**
   * 创建动画片段
   * @param options 动画片段选项
   */
  constructor(options: AnimationClipOptions = {}) {
    this.name = options.name || '动画';
    this.tracks = options.tracks || [];
    this.duration = options.duration || 0;
    this.loopMode = options.loopMode || LoopMode.REPEAT;
    this.speed = options.speed || 1.0;
    this.enabled = options.enabled !== undefined ? options.enabled : true;
    this.weight = options.weight || 1.0;
    this.blendTime = options.blendTime || 0.3;

    // 如果没有指定持续时间，则从轨道中计算
    if (this.duration === 0 && this.tracks.length > 0) {
      this.calculateDuration();
    }
  }

  /**
   * 从轨道计算动画持续时间
   */
  private calculateDuration(): void {
    let maxTime = 0;

    for (const track of this.tracks) {
      if (track.keyframes.length > 0) {
        const lastKeyframe = track.keyframes[track.keyframes.length - 1];
        maxTime = Math.max(maxTime, lastKeyframe.time);
      }
    }

    this.duration = maxTime;
  }

  /**
   * 添加动画轨道
   * @param track 动画轨道
   */
  public addTrack(track: AnimationTrack): void {
    this.tracks.push(track);
    this.calculateDuration();
    this._threeClip = null; // 清除缓存的Three.js动画片段
  }

  /**
   * 移除动画轨道
   * @param index 轨道索引
   * @returns 是否成功移除
   */
  public removeTrack(index: number): boolean {
    if (index >= 0 && index < this.tracks.length) {
      this.tracks.splice(index, 1);
      this.calculateDuration();
      this._threeClip = null; // 清除缓存的Three.js动画片段
      return true;
    }

    return false;
  }

  /**
   * 获取指定类型和目标的轨道
   * @param type 轨道类型
   * @param targetPath 目标路径
   * @returns 匹配的轨道，如果不存在则返回null
   */
  public getTrack(type: TrackType, targetPath: string): AnimationTrack | null {
    for (const track of this.tracks) {
      if (track.type === type && track.targetPath === targetPath) {
        return track;
      }
    }

    return null;
  }

  /**
   * 获取指定时间点的动画状态
   * @param time 时间点（秒）
   * @param result 结果对象，用于存储计算结果
   * @returns 动画状态映射，键为目标路径，值为计算的属性值
   */
  public getStateAtTime(time: number, result: Map<string, any> = new Map()): Map<string, any> {
    // 处理循环
    let adjustedTime = time;
    if (this.duration > 0) {
      if (this.loopMode === LoopMode.REPEAT) {
        adjustedTime = time % this.duration;
      } else if (this.loopMode === LoopMode.PING_PONG) {
        const cycle = Math.floor(time / this.duration);
        adjustedTime = time % this.duration;
        if (cycle % 2 === 1) {
          adjustedTime = this.duration - adjustedTime;
        }
      } else if (time > this.duration) {
        adjustedTime = this.duration;
      }
    }

    // 使用缓存来避免重复计算
    if (this._cacheEnabled && this._cachedTime === adjustedTime && this._cachedResult) {
      // 复制缓存的结果
      for (const [key, value] of this._cachedResult) {
        result.set(key, this._cloneValue(value));
      }
    } else {
      // 计算每个轨道在当前时间的值
      for (const track of this.tracks) {
        const value = this.evaluateTrack(track, adjustedTime);
        result.set(track.targetPath, value);
      }

      // 更新缓存
      if (this._cacheEnabled) {
        this._cachedTime = adjustedTime;
        this._cachedResult = new Map();
        for (const [key, value] of result) {
          this._cachedResult.set(key, this._cloneValue(value));
        }
      }
    }

    return result;
  }

  /**
   * 设置是否启用缓存
   * @param enabled 是否启用
   */
  public setCacheEnabled(enabled: boolean): void {
    this._cacheEnabled = enabled;
    if (!enabled) {
      this._cachedTime = -1;
      this._cachedResult = null;
    }
  }

  /**
   * 清除缓存
   */
  public clearCache(): void {
    this._cachedTime = -1;
    this._cachedResult = null;
  }

  /**
   * 克隆值
   * @param value 要克隆的值
   * @returns 克隆的值
   */
  private _cloneValue(value: any): any {
    if (value instanceof THREE.Vector2) {
      return value.clone();
    } else if (value instanceof THREE.Vector3) {
      return value.clone();
    } else if (value instanceof THREE.Vector4) {
      return value.clone();
    } else if (value instanceof THREE.Quaternion) {
      return value.clone();
    } else if (value instanceof THREE.Color) {
      return value.clone();
    } else if (value instanceof THREE.Matrix4) {
      return value.clone();
    } else {
      return value;
    }
  }

  /**
   * 计算轨道在指定时间点的值
   * @param track 动画轨道
   * @param time 时间点（秒）
   * @returns 计算的值
   */
  private evaluateTrack(track: AnimationTrack, time: number): any {
    const keyframes = track.keyframes;

    // 如果没有关键帧，返回默认值
    if (keyframes.length === 0) {
      return this.getDefaultValueForTrackType(track.type);
    }

    // 如果只有一个关键帧，直接返回其值
    if (keyframes.length === 1) {
      return keyframes[0].value;
    }

    // 如果时间小于第一个关键帧的时间，返回第一个关键帧的值
    if (time <= keyframes[0].time) {
      return keyframes[0].value;
    }

    // 如果时间大于最后一个关键帧的时间，返回最后一个关键帧的值
    if (time >= keyframes[keyframes.length - 1].time) {
      return keyframes[keyframes.length - 1].value;
    }

    // 找到时间所在的两个关键帧
    let startIndex = 0;
    for (let i = 0; i < keyframes.length - 1; i++) {
      if (time >= keyframes[i].time && time < keyframes[i + 1].time) {
        startIndex = i;
        break;
      }
    }

    const startKeyframe = keyframes[startIndex];
    const endKeyframe = keyframes[startIndex + 1];

    // 计算两个关键帧之间的插值因子
    const t = (time - startKeyframe.time) / (endKeyframe.time - startKeyframe.time);

    // 根据插值类型计算值
    const interpolationType = endKeyframe.interpolation || track.defaultInterpolation;

    return this.interpolate(
      startKeyframe.value,
      endKeyframe.value,
      t,
      interpolationType,
      track.type,
      startKeyframe.outTangent,
      endKeyframe.inTangent
    );
  }

  /**
   * 根据轨道类型获取默认值
   * @param type 轨道类型
   * @returns 默认值
   */
  private getDefaultValueForTrackType(type: TrackType): any {
    switch (type) {
      case TrackType.POSITION:
      case TrackType.SCALE:
        return new THREE.Vector3(0, 0, 0);
      case TrackType.ROTATION:
        return new THREE.Quaternion();
      case TrackType.COLOR:
        return new THREE.Color(1, 1, 1);
      case TrackType.OPACITY:
      case TrackType.WEIGHT:
        return 1;
      case TrackType.VISIBILITY:
        return true;
      case TrackType.MATRIX:
        return new THREE.Matrix4();
      case TrackType.MORPH_TARGET:
        return 0;
      default:
        return null;
    }
  }

  /**
   * 在两个值之间进行插值
   * @param start 起始值
   * @param end 结束值
   * @param t 插值因子（0-1）
   * @param interpolationType 插值类型
   * @param trackType 轨道类型
   * @param outTangent 出切线
   * @param inTangent 入切线
   * @returns 插值结果
   */
  private interpolate(
    start: any,
    end: any,
    t: number,
    interpolationType: InterpolationType,
    trackType: TrackType,
    outTangent?: any,
    inTangent?: any
  ): any {
    // 根据插值类型选择不同的插值方法
    switch (interpolationType) {
      case InterpolationType.STEP:
        return this.stepInterpolate(start, end, t);
      case InterpolationType.CUBIC:
        return this.cubicInterpolate(start, end, t, outTangent, inTangent, trackType);
      case InterpolationType.BEZIER:
        return this.bezierInterpolate(start, end, t, outTangent, inTangent, trackType);
      case InterpolationType.LINEAR:
      default:
        return this.linearInterpolate(start, end, t, trackType);
    }
  }

  /**
   * 线性插值
   * @param start 起始值
   * @param end 结束值
   * @param t 插值因子（0-1）
   * @param trackType 轨道类型
   * @returns 插值结果
   */
  private linearInterpolate(start: any, end: any, t: number, trackType: TrackType): any {
    // 根据轨道类型选择不同的线性插值方法
    switch (trackType) {
      case TrackType.POSITION:
      case TrackType.SCALE:
        // 向量线性插值
        if (start instanceof THREE.Vector3 && end instanceof THREE.Vector3) {
          return new THREE.Vector3().copy(start).lerp(end, t);
        }
        break;

      case TrackType.ROTATION:
        // 四元数球面线性插值
        if (start instanceof THREE.Quaternion && end instanceof THREE.Quaternion) {
          return new THREE.Quaternion().copy(start).slerp(end, t);
        }
        break;

      case TrackType.COLOR:
        // 颜色线性插值
        if (start instanceof THREE.Color && end instanceof THREE.Color) {
          return new THREE.Color().copy(start).lerp(end, t);
        }
        break;

      case TrackType.OPACITY:
      case TrackType.WEIGHT:
      case TrackType.MORPH_TARGET:
        // 数值线性插值
        if (typeof start === 'number' && typeof end === 'number') {
          return start + (end - start) * t;
        }
        break;

      case TrackType.VISIBILITY:
        // 布尔值阶梯插值
        return t < 0.5 ? start : end;

      case TrackType.MATRIX:
        // 矩阵插值（通过分解为位置、旋转和缩放）
        if (start instanceof THREE.Matrix4 && end instanceof THREE.Matrix4) {
          const startPos = new THREE.Vector3();
          const startRot = new THREE.Quaternion();
          const startScale = new THREE.Vector3();
          start.decompose(startPos, startRot, startScale);

          const endPos = new THREE.Vector3();
          const endRot = new THREE.Quaternion();
          const endScale = new THREE.Vector3();
          end.decompose(endPos, endRot, endScale);

          const pos = new THREE.Vector3().copy(startPos).lerp(endPos, t);
          const rot = new THREE.Quaternion().copy(startRot).slerp(endRot, t);
          const scale = new THREE.Vector3().copy(startScale).lerp(endScale, t);

          return new THREE.Matrix4().compose(pos, rot, scale);
        }
        break;

      default:
        // 默认情况下，如果是对象且有lerp方法，则使用lerp
        if (start && end && typeof start.lerp === 'function') {
          return start.clone().lerp(end, t);
        }
        // 否则，如果是数字，则进行线性插值
        else if (typeof start === 'number' && typeof end === 'number') {
          return start + (end - start) * t;
        }
        // 如果是其他类型，则根据t选择起始值或结束值
        else {
          return t < 0.5 ? start : end;
        }
    }

    // 如果无法插值，则返回结束值
    return end;
  }

  /**
   * 阶梯插值
   * @param start 起始值
   * @param end 结束值
   * @param t 插值因子（0-1）
   * @returns 插值结果
   */
  private stepInterpolate(start: any, end: any, t: number): any {
    // 阶梯插值直接返回起始值，直到t=1时返回结束值
    return t < 1.0 ? start : end;
  }

  /**
   * 三次样条插值
   * @param start 起始值
   * @param end 结束值
   * @param t 插值因子（0-1）
   * @param outTangent 出切线
   * @param inTangent 入切线
   * @param trackType 轨道类型
   * @returns 插值结果
   */
  private cubicInterpolate(
    start: any,
    end: any,
    t: number,
    outTangent: any,
    inTangent: any,
    trackType: TrackType
  ): any {
    // 如果没有切线信息，则回退到线性插值
    if (!outTangent || !inTangent) {
      return this.linearInterpolate(start, end, t, trackType);
    }

    // 根据轨道类型选择不同的三次样条插值方法
    switch (trackType) {
      case TrackType.POSITION:
      case TrackType.SCALE:
        // 向量三次样条插值
        if (start instanceof THREE.Vector3 && end instanceof THREE.Vector3) {
          const t2 = t * t;
          const t3 = t2 * t;

          // 三次Hermite多项式系数
          const h1 = 2 * t3 - 3 * t2 + 1;
          const h2 = -2 * t3 + 3 * t2;
          const h3 = t3 - 2 * t2 + t;
          const h4 = t3 - t2;

          // 计算插值结果
          const result = new THREE.Vector3();
          result.x = h1 * start.x + h2 * end.x + h3 * outTangent.x + h4 * inTangent.x;
          result.y = h1 * start.y + h2 * end.y + h3 * outTangent.y + h4 * inTangent.y;
          result.z = h1 * start.z + h2 * end.z + h3 * outTangent.z + h4 * inTangent.z;

          return result;
        }
        break;

      case TrackType.OPACITY:
      case TrackType.WEIGHT:
      case TrackType.MORPH_TARGET:
        // 数值三次样条插值
        if (typeof start === 'number' && typeof end === 'number') {
          const t2 = t * t;
          const t3 = t2 * t;

          // 三次Hermite多项式系数
          const h1 = 2 * t3 - 3 * t2 + 1;
          const h2 = -2 * t3 + 3 * t2;
          const h3 = t3 - 2 * t2 + t;
          const h4 = t3 - t2;

          // 计算插值结果
          return h1 * start + h2 * end + h3 * outTangent + h4 * inTangent;
        }
        break;

      default:
        // 其他类型回退到线性插值
        return this.linearInterpolate(start, end, t, trackType);
    }

    // 如果无法插值，则回退到线性插值
    return this.linearInterpolate(start, end, t, trackType);
  }

  /**
   * 贝塞尔曲线插值
   * @param start 起始值
   * @param end 结束值
   * @param t 插值因子（0-1）
   * @param outTangent 出切线（控制点1）
   * @param inTangent 入切线（控制点2）
   * @param trackType 轨道类型
   * @returns 插值结果
   */
  private bezierInterpolate(
    start: any,
    end: any,
    t: number,
    outTangent: any,
    inTangent: any,
    trackType: TrackType
  ): any {
    // 如果没有切线信息，则回退到线性插值
    if (!outTangent || !inTangent) {
      return this.linearInterpolate(start, end, t, trackType);
    }

    // 根据轨道类型选择不同的贝塞尔曲线插值方法
    switch (trackType) {
      case TrackType.POSITION:
      case TrackType.SCALE:
        // 向量贝塞尔曲线插值
        if (start instanceof THREE.Vector3 && end instanceof THREE.Vector3) {
          const t2 = t * t;
          const t3 = t2 * t;
          const mt = 1 - t;
          const mt2 = mt * mt;
          const mt3 = mt2 * mt;

          // 三次贝塞尔曲线公式
          const b0 = mt3;
          const b1 = 3 * mt2 * t;
          const b2 = 3 * mt * t2;
          const b3 = t3;

          // 计算插值结果
          const result = new THREE.Vector3();
          result.x = b0 * start.x + b1 * outTangent.x + b2 * inTangent.x + b3 * end.x;
          result.y = b0 * start.y + b1 * outTangent.y + b2 * inTangent.y + b3 * end.y;
          result.z = b0 * start.z + b1 * outTangent.z + b2 * inTangent.z + b3 * end.z;

          return result;
        }
        break;

      case TrackType.OPACITY:
      case TrackType.WEIGHT:
      case TrackType.MORPH_TARGET:
        // 数值贝塞尔曲线插值
        if (typeof start === 'number' && typeof end === 'number') {
          const t2 = t * t;
          const t3 = t2 * t;
          const mt = 1 - t;
          const mt2 = mt * mt;
          const mt3 = mt2 * mt;

          // 三次贝塞尔曲线公式
          const b0 = mt3;
          const b1 = 3 * mt2 * t;
          const b2 = 3 * mt * t2;
          const b3 = t3;

          // 计算插值结果
          return b0 * start + b1 * outTangent + b2 * inTangent + b3 * end;
        }
        break;

      default:
        // 其他类型回退到线性插值
        return this.linearInterpolate(start, end, t, trackType);
    }

    // 如果无法插值，则回退到线性插值
    return this.linearInterpolate(start, end, t, trackType);
  }

  /**
   * 从Three.js动画片段创建动画片段
   * @param threeClip Three.js动画片段
   * @returns 创建的动画片段
   */
  public static fromThreeAnimationClip(threeClip: THREE.AnimationClip): AnimationClip {
    // 创建动画片段选项
    const options: AnimationClipOptions = {
      name: threeClip.name,
      duration: threeClip.duration,
      tracks: []
    };

    // 转换轨道
    for (const track of threeClip.tracks) {
      // 解析目标路径和属性
      const trackNameParts = track.name.split('.');
      const targetPath = trackNameParts[0];
      const propertyName = trackNameParts[1];

      // 确定轨道类型
      let trackType: TrackType;
      switch (propertyName) {
        case 'position':
          trackType = TrackType.POSITION;
          break;
        case 'quaternion':
        case 'rotation':
          trackType = TrackType.ROTATION;
          break;
        case 'scale':
          trackType = TrackType.SCALE;
          break;
        case 'visible':
          trackType = TrackType.VISIBILITY;
          break;
        case 'opacity':
          trackType = TrackType.OPACITY;
          break;
        case 'color':
          trackType = TrackType.COLOR;
          break;
        case 'morphTargetInfluences':
          trackType = TrackType.MORPH_TARGET;
          break;
        default:
          trackType = TrackType.CUSTOM;
      }

      // 创建关键帧
      const keyframes: Keyframe[] = [];
      const times = track.times;
      const values = track.values;

      // 根据轨道类型处理值
      if (track.getValueSize) {
        const valueSize = track.getValueSize();

        for (let i = 0; i < times.length; i++) {
          const time = times[i];
          const valueIndex = i * valueSize;

          let value: any;

          // 根据轨道类型和值大小创建适当的值对象
          if (trackType === TrackType.POSITION || trackType === TrackType.SCALE) {
            value = new THREE.Vector3(
              values[valueIndex],
              values[valueIndex + 1],
              values[valueIndex + 2]
            );
          } else if (trackType === TrackType.ROTATION) {
            if (valueSize === 4) {
              // 四元数
              value = new THREE.Quaternion(
                values[valueIndex],
                values[valueIndex + 1],
                values[valueIndex + 2],
                values[valueIndex + 3]
              );
            } else {
              // 欧拉角
              value = new THREE.Vector3(
                values[valueIndex],
                values[valueIndex + 1],
                values[valueIndex + 2]
              );
            }
          } else if (trackType === TrackType.COLOR) {
            value = new THREE.Color(
              values[valueIndex],
              values[valueIndex + 1],
              values[valueIndex + 2]
            );
          } else if (valueSize === 1) {
            // 单值（如不透明度、可见性等）
            value = values[valueIndex];
          } else {
            // 其他多值属性
            value = [];
            for (let j = 0; j < valueSize; j++) {
              value.push(values[valueIndex + j]);
            }
          }

          keyframes.push({
            time,
            value,
            interpolation: InterpolationType.LINEAR
          });
        }
      }

      // 添加轨道
      if (keyframes.length > 0) {
        if (!options.tracks) {
          options.tracks = [];
        }
        options.tracks.push({
          type: trackType,
          targetPath: targetPath,
          keyframes: keyframes,
          defaultInterpolation: InterpolationType.LINEAR
        });
      }
    }

    // 创建动画片段
    return new AnimationClip(options);
  }
}