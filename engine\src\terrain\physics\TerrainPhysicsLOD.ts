/**
 * 地形物理LOD系统
 * 根据距离动态调整地形物理精度
 */
import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { type Camera  } from '../../rendering/Camera';
import { TerrainComponent } from '../components/TerrainComponent';
import { Debug } from '../../utils/Debug';
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 地形物理LOD级别
 */
export enum TerrainPhysicsLODLevel {
  /** 高精度 */
  HIGH = 0,
  /** 中精度 */
  MEDIUM = 1,
  /** 低精度 */
  LOW = 2,
  /** 极低精度 */
  VERY_LOW = 3,
  /** 禁用物理 */
  DISABLED = 4
}

/**
 * 地形物理LOD配置
 */
export interface TerrainPhysicsLODConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否自动更新 */
  autoUpdate?: boolean;
  /** 更新频率 */
  updateFrequency?: number;
  /** 高精度距离 */
  highLevelDistance?: number;
  /** 中精度距离 */
  mediumLevelDistance?: number;
  /** 低精度距离 */
  lowLevelDistance?: number;
  /** 极低精度距离 */
  veryLowLevelDistance?: number;
  /** 高精度分辨率 */
  highLevelResolution?: number;
  /** 中精度分辨率 */
  mediumLevelResolution?: number;
  /** 低精度分辨率 */
  lowLevelResolution?: number;
  /** 极低精度分辨率 */
  veryLowLevelResolution?: number;
  /** 是否使用调试可视化 */
  useDebugVisualization?: boolean;
}

/**
 * 地形物理LOD事件类型
 */
export enum TerrainPhysicsLODEventType {
  /** LOD级别变更 */
  LOD_LEVEL_CHANGED = 'lod_level_changed'
}

/**
 * 地形物理LOD状态
 */
interface TerrainPhysicsLODState {
  /** 当前LOD级别 */
  currentLevel: TerrainPhysicsLODLevel;
  /** 当前分辨率 */
  currentResolution: number;
  /** 是否需要更新 */
  needsUpdate: boolean;
}

/**
 * 地形物理LOD系统
 */
export class TerrainPhysicsLOD {
  /** 是否启用 */
  private enabled: boolean;
  /** 是否自动更新 */
  private autoUpdate: boolean;
  /** 更新频率 */
  private updateFrequency: number;
  /** 高精度距离 */
  private highLevelDistance: number;
  /** 中精度距离 */
  private mediumLevelDistance: number;
  /** 低精度距离 */
  private lowLevelDistance: number;
  /** 极低精度距离 */
  private veryLowLevelDistance: number;
  /** 高精度分辨率 */
  private highLevelResolution: number;
  /** 中精度分辨率 */
  private mediumLevelResolution: number;
  /** 低精度分辨率 */
  private lowLevelResolution: number;
  /** 极低精度分辨率 */
  private veryLowLevelResolution: number;
  /** 是否使用调试可视化 */
  private useDebugVisualization: boolean;

  /** 地形实体映射 */
  private terrainEntities: Map<Entity, TerrainComponent>;
  /** LOD状态映射 */
  private lodStates: Map<string, TerrainPhysicsLODState>;
  /** 帧计数器 */
  private frameCount: number;
  /** 事件发射器 */
  private eventEmitter: EventEmitter;
  /** 调试网格 */
  private debugMeshes: THREE.Mesh[];
  /** 活跃相机 */
  private activeCamera: Camera | null;

  /**
   * 创建地形物理LOD系统
   * @param config 配置
   */
  constructor(config: TerrainPhysicsLODConfig = {}) {
    this.enabled = config.enabled !== undefined ? config.enabled : true;
    this.autoUpdate = config.autoUpdate !== undefined ? config.autoUpdate : true;
    this.updateFrequency = config.updateFrequency || 10; // 每10帧更新一次
    this.highLevelDistance = config.highLevelDistance || 50;
    this.mediumLevelDistance = config.mediumLevelDistance || 150;
    this.lowLevelDistance = config.lowLevelDistance || 300;
    this.veryLowLevelDistance = config.veryLowLevelDistance || 600;
    this.highLevelResolution = config.highLevelResolution || 128;
    this.mediumLevelResolution = config.mediumLevelResolution || 64;
    this.lowLevelResolution = config.lowLevelResolution || 32;
    this.veryLowLevelResolution = config.veryLowLevelResolution || 16;
    this.useDebugVisualization = config.useDebugVisualization || false;

    this.terrainEntities = new Map();
    this.lodStates = new Map();
    this.frameCount = 0;
    this.eventEmitter = new EventEmitter();
    this.debugMeshes = [];
    this.activeCamera = null;
  }

  /**
   * 添加地形实体
   * @param entity 实体
   * @param component 地形组件
   */
  public addTerrainEntity(entity: Entity, component: TerrainComponent): void {
    // 存储地形实体
    this.terrainEntities.set(entity, component);

    // 创建LOD状态
    this.lodStates.set(entity.id, {
      currentLevel: TerrainPhysicsLODLevel.HIGH,
      currentResolution: this.highLevelResolution,
      needsUpdate: true
    });

    Debug.log('TerrainPhysicsLOD', `添加地形实体: ${entity.id}`);
  }

  /**
   * 移除地形实体
   * @param entity 实体
   */
  public removeTerrainEntity(entity: Entity): void {
    // 移除地形实体
    this.terrainEntities.delete(entity);

    // 移除LOD状态
    this.lodStates.delete(entity.id);

    Debug.log('TerrainPhysicsLOD', `移除地形实体: ${entity.id}`);
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.enabled || !this.autoUpdate) {
      return;
    }

    // 按照更新频率更新
    this.frameCount++;
    if (this.frameCount % this.updateFrequency !== 0) {
      return;
    }

    // 获取相机
    const camera = this.getCamera();
    if (!camera) {
      return;
    }

    // 更新所有地形实体
    this.updateTerrainEntities(camera);

    // 更新调试可视化
    if (this.useDebugVisualization) {
      this.updateDebugVisualization();
    }
  }

  /**
   * 获取相机
   * @returns 相机
   */
  private getCamera(): Camera | null {
    return this.activeCamera;
  }

  /**
   * 设置活跃相机
   * @param camera 相机
   */
  public setActiveCamera(camera: Camera): void {
    this.activeCamera = camera;
  }

  /**
   * 更新所有地形实体
   * @param camera 相机
   */
  private updateTerrainEntities(camera: Camera): void {
    const cameraPosition = camera.getThreeCamera().position;

    // 遍历所有地形实体
    for (const [entity, component] of this.terrainEntities.entries()) {
      // 获取实体位置
      const entityPosition = entity.getWorldPosition(new THREE.Vector3());

      // 计算距离
      const distance = cameraPosition.distanceTo(entityPosition);

      // 确定LOD级别
      const level = this.determineLODLevel(distance);

      // 获取分辨率
      const resolution = this.getResolutionForLevel(level);

      // 获取LOD状态
      const state = this.lodStates.get(entity.id);
      if (!state) {
        continue;
      }

      // 如果级别或分辨率变化，则更新
      if (state.currentLevel !== level || state.currentResolution !== resolution) {
        // 更新状态
        state.currentLevel = level;
        state.currentResolution = resolution;
        state.needsUpdate = true;

        // 更新地形组件
        this.updateTerrainComponent(entity, component, level, resolution);

        // 发出事件
        this.eventEmitter.emit(TerrainPhysicsLODEventType.LOD_LEVEL_CHANGED, entity, level, resolution);
      }
    }
  }

  /**
   * 确定LOD级别
   * @param distance 距离
   * @returns LOD级别
   */
  private determineLODLevel(distance: number): TerrainPhysicsLODLevel {
    if (distance <= this.highLevelDistance) {
      return TerrainPhysicsLODLevel.HIGH;
    } else if (distance <= this.mediumLevelDistance) {
      return TerrainPhysicsLODLevel.MEDIUM;
    } else if (distance <= this.lowLevelDistance) {
      return TerrainPhysicsLODLevel.LOW;
    } else if (distance <= this.veryLowLevelDistance) {
      return TerrainPhysicsLODLevel.VERY_LOW;
    } else {
      return TerrainPhysicsLODLevel.DISABLED;
    }
  }

  /**
   * 获取级别对应的分辨率
   * @param level LOD级别
   * @returns 分辨率
   */
  private getResolutionForLevel(level: TerrainPhysicsLODLevel): number {
    switch (level) {
      case TerrainPhysicsLODLevel.HIGH:
        return this.highLevelResolution;
      case TerrainPhysicsLODLevel.MEDIUM:
        return this.mediumLevelResolution;
      case TerrainPhysicsLODLevel.LOW:
        return this.lowLevelResolution;
      case TerrainPhysicsLODLevel.VERY_LOW:
        return this.veryLowLevelResolution;
      case TerrainPhysicsLODLevel.DISABLED:
        return 0;
      default:
        return this.highLevelResolution;
    }
  }

  /**
   * 更新地形组件
   * @param entity 实体
   * @param component 地形组件
   * @param level LOD级别
   * @param resolution 分辨率
   */
  private updateTerrainComponent(
    entity: Entity,
    component: TerrainComponent,
    level: TerrainPhysicsLODLevel,
    resolution: number
  ): void {
    // 如果级别为禁用，则禁用物理
    if (level === TerrainPhysicsLODLevel.DISABLED) {
      component.usePhysics = false;
      component.needsPhysicsUpdate = true;
      return;
    }

    // 启用物理
    component.usePhysics = true;

    // 更新物理分辨率
    component.physicsResolution = resolution;

    // 标记需要更新物理
    component.needsPhysicsUpdate = true;

    Debug.log('TerrainPhysicsLOD', `更新地形物理LOD: ${entity.id}, 级别: ${level}, 分辨率: ${resolution}`);
  }

  /**
   * 更新调试可视化
   */
  private updateDebugVisualization(): void {
    // 清除现有的调试网格
    this.clearDebugMeshes();

    // 创建材质
    const highMaterial = new THREE.MeshBasicMaterial({
      color: 0x00ff00,
      wireframe: true,
      transparent: true,
      opacity: 0.5
    });

    const mediumMaterial = new THREE.MeshBasicMaterial({
      color: 0xffff00,
      wireframe: true,
      transparent: true,
      opacity: 0.5
    });

    const lowMaterial = new THREE.MeshBasicMaterial({
      color: 0xff8800,
      wireframe: true,
      transparent: true,
      opacity: 0.5
    });

    const veryLowMaterial = new THREE.MeshBasicMaterial({
      color: 0xff0000,
      wireframe: true,
      transparent: true,
      opacity: 0.5
    });

    const disabledMaterial = new THREE.MeshBasicMaterial({
      color: 0x888888,
      wireframe: true,
      transparent: true,
      opacity: 0.2
    });

    // 遍历所有地形实体
    for (const [entity, component] of this.terrainEntities.entries()) {
      // 获取LOD状态
      const state = this.lodStates.get(entity.id);
      if (!state) {
        continue;
      }

      // 选择材质
      let material;
      switch (state.currentLevel) {
        case TerrainPhysicsLODLevel.HIGH:
          material = highMaterial;
          break;
        case TerrainPhysicsLODLevel.MEDIUM:
          material = mediumMaterial;
          break;
        case TerrainPhysicsLODLevel.LOW:
          material = lowMaterial;
          break;
        case TerrainPhysicsLODLevel.VERY_LOW:
          material = veryLowMaterial;
          break;
        case TerrainPhysicsLODLevel.DISABLED:
          material = disabledMaterial;
          break;
        default:
          material = highMaterial;
      }

      // 创建平面几何体
      const geometry = new THREE.PlaneGeometry(
        component.width,
        component.height,
        state.currentResolution > 0 ? state.currentResolution - 1 : 1,
        state.currentResolution > 0 ? state.currentResolution - 1 : 1
      );

      // 旋转几何体
      geometry.rotateX(-Math.PI / 2);

      // 创建网格
      const mesh = new THREE.Mesh(geometry, material);

      // 设置位置
      mesh.position.copy(entity.getWorldPosition(new THREE.Vector3()));

      // 添加到实体
      entity.add(mesh);

      // 存储调试网格
      this.debugMeshes.push(mesh);
    }
  }

  /**
   * 清除调试网格
   */
  private clearDebugMeshes(): void {
    // 移除所有调试网格
    for (const mesh of this.debugMeshes) {
      if (mesh.parent) {
        mesh.parent.remove(mesh);
      }

      // 释放资源
      (mesh.geometry as any).dispose();

      if (Array.isArray(mesh.material)) {
        for (const material of mesh.material) {
          (material as any).dispose();
        }
      } else {
        (mesh.material as any).dispose();
      }
    }

    // 清空数组
    this.debugMeshes = [];
  }

  /**
   * 设置配置
   * @param config 配置
   */
  public setConfig(config: TerrainPhysicsLODConfig): void {
    this.enabled = config.enabled !== undefined ? config.enabled : this.enabled;
    this.autoUpdate = config.autoUpdate !== undefined ? config.autoUpdate : this.autoUpdate;
    this.updateFrequency = config.updateFrequency || this.updateFrequency;
    this.highLevelDistance = config.highLevelDistance || this.highLevelDistance;
    this.mediumLevelDistance = config.mediumLevelDistance || this.mediumLevelDistance;
    this.lowLevelDistance = config.lowLevelDistance || this.lowLevelDistance;
    this.veryLowLevelDistance = config.veryLowLevelDistance || this.veryLowLevelDistance;
    this.highLevelResolution = config.highLevelResolution || this.highLevelResolution;
    this.mediumLevelResolution = config.mediumLevelResolution || this.mediumLevelResolution;
    this.lowLevelResolution = config.lowLevelResolution || this.lowLevelResolution;
    this.veryLowLevelResolution = config.veryLowLevelResolution || this.veryLowLevelResolution;
    this.useDebugVisualization = config.useDebugVisualization !== undefined ? config.useDebugVisualization : this.useDebugVisualization;

    // 如果启用调试可视化，则更新
    if (this.useDebugVisualization) {
      this.updateDebugVisualization();
    } else {
      this.clearDebugMeshes();
    }
  }

  /**
   * 获取配置
   * @returns 配置
   */
  public getConfig(): TerrainPhysicsLODConfig {
    return {
      enabled: this.enabled,
      autoUpdate: this.autoUpdate,
      updateFrequency: this.updateFrequency,
      highLevelDistance: this.highLevelDistance,
      mediumLevelDistance: this.mediumLevelDistance,
      lowLevelDistance: this.lowLevelDistance,
      veryLowLevelDistance: this.veryLowLevelDistance,
      highLevelResolution: this.highLevelResolution,
      mediumLevelResolution: this.mediumLevelResolution,
      lowLevelResolution: this.lowLevelResolution,
      veryLowLevelResolution: this.veryLowLevelResolution,
      useDebugVisualization: this.useDebugVisualization
    };
  }

  /**
   * 注册事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public on(event: TerrainPhysicsLODEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public off(event: TerrainPhysicsLODEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }
}
