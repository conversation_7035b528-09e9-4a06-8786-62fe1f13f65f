/**
 * 地形生成算法
 * 包含各种地形生成算法的实现
 */
import * as THREE from 'three';
import { TerrainComponent } from '../components/TerrainComponent';
import { Debug } from '../../utils/Debug';
import {
  TerrainGenerationWorkerManager,
  TerrainGenerationWorkerManagerConfig
} from '../workers/TerrainGenerationWorkerManager';
import {
  TerrainWorkerMessageType
} from '../workers/TerrainGenerationWorker';
import {
  UndergroundRiverGenerator,
  UndergroundRiverParams
} from './UndergroundRiverGenerator';
import {
  UndergroundLakeGenerator,
  UndergroundLakeParams
} from './UndergroundLakeGenerator';

/**
 * 侵蚀类型
 */
export enum ErosionType {
  /** 热侵蚀 */
  THERMAL = 'thermal',
  /** 水侵蚀 */
  HYDRAULIC = 'hydraulic'
}

/**
 * 地形特征类型
 */
export enum TerrainFeatureType {
  /** 山脉 */
  MOUNTAIN = 'mountain',
  /** 峡谷 */
  CANYON = 'canyon',
  /** 河流 */
  RIVER = 'river',
  /** 洞穴 */
  CAVE = 'cave',
  /** 悬崖 */
  CLIFF = 'cliff',
  /** 平原 */
  PLAIN = 'plain',
  /** 丘陵 */
  HILLS = 'hills',
  /** 沙漠 */
  DESERT = 'desert',
  /** 火山 */
  VOLCANO = 'volcano',
  /** 岛屿 */
  ISLAND = 'island',
  /** 地下河 */
  UNDERGROUND_RIVER = 'underground_river',
  /** 地下湖泊 */
  UNDERGROUND_LAKE = 'underground_lake'
}

/**
 * 热侵蚀参数
 */
export interface ThermalErosionParams {
  /** 侵蚀迭代次数 */
  iterations: number;
  /** 侵蚀强度 */
  strength: number;
  /** 坡度阈值 */
  slopeThreshold: number;
  /** 沉积系数 */
  depositionRate: number;
}

/**
 * 水侵蚀参数
 */
export interface HydraulicErosionParams {
  /** 侵蚀迭代次数 */
  iterations: number;
  /** 雨滴数量 */
  droplets: number;
  /** 雨滴容量 */
  capacity: number;
  /** 侵蚀强度 */
  erosionStrength: number;
  /** 沉积强度 */
  depositionStrength: number;
  /** 蒸发率 */
  evaporationRate: number;
  /** 惯性 */
  inertia: number;
  /** 最小坡度 */
  minSlope: number;
  /** 重力 */
  gravity: number;
}

/**
 * 河流生成参数
 */
export interface RiverGenerationParams {
  /** 河流数量 */
  count: number;
  /** 河流宽度 */
  width: number;
  /** 河流深度 */
  depth: number;
  /** 河流曲折度 */
  sinuosity: number;
  /** 河流分支概率 */
  branchProbability: number;
  /** 最小长度 */
  minLength: number;
  /** 最大长度 */
  maxLength: number;
  /** 种子 */
  seed: number;
}

/**
 * 山脉生成参数
 */
export interface MountainGenerationParams {
  /** 山脉数量 */
  count: number;
  /** 山脉高度 */
  height: number;
  /** 山脉宽度 */
  width: number;
  /** 山脉粗糙度 */
  roughness: number;
  /** 山脉锐度 */
  sharpness: number;
  /** 种子 */
  seed: number;
}

/**
 * 峡谷生成参数
 */
export interface CanyonGenerationParams {
  /** 峡谷数量 */
  count: number;
  /** 峡谷深度 */
  depth: number;
  /** 峡谷宽度 */
  width: number;
  /** 峡谷曲折度 */
  sinuosity: number;
  /** 峡谷粗糙度 */
  roughness: number;
  /** 种子 */
  seed: number;
}

/**
 * 洞穴生成参数
 */
export interface CaveGenerationParams {
  /** 洞穴数量 */
  count: number;
  /** 洞穴大小 */
  size: number;
  /** 洞穴深度 */
  depth: number;
  /** 洞穴复杂度 */
  complexity: number;
  /** 连接概率 */
  connectionProbability: number;
  /** 随机种子 */
  seed: number;
}

/**
 * 悬崖生成参数
 */
export interface CliffGenerationParams {
  /** 悬崖数量 */
  count: number;
  /** 悬崖高度 */
  height: number;
  /** 悬崖宽度 */
  width: number;
  /** 悬崖陡峭度 */
  steepness: number;
  /** 悬崖粗糙度 */
  roughness: number;
  /** 随机种子 */
  seed: number;
}

/**
 * 火山生成参数
 */
export interface VolcanoGenerationParams {
  /** 火山数量 */
  count: number;
  /** 火山高度 */
  height: number;
  /** 火山半径 */
  radius: number;
  /** 火山口大小 */
  craterSize: number;
  /** 火山口深度 */
  craterDepth: number;
  /** 随机种子 */
  seed: number;
}

/**
 * 地形特征组合生成参数
 */
export interface TerrainFeatureCombinationParams {
  /** 基础地形类型 */
  baseTerrainType: TerrainFeatureType;
  /** 基础地形参数 */
  baseTerrainParams: any;
  /** 特征列表 */
  features: {
    /** 特征类型 */
    type: TerrainFeatureType;
    /** 特征参数 */
    params: any;
    /** 权重 */
    weight: number;
  }[];
  /** 随机种子 */
  seed: number;
}

/**
 * 地形生成算法类
 */
export class TerrainGenerationAlgorithms {
  /** 工作线程管理器 */
  private static workerManager: TerrainGenerationWorkerManager | null = null;
  /** 是否启用多线程 */
  private static enableMultithreading: boolean = true;
  /** 是否已初始化 */
  private static initialized: boolean = false;

  /**
   * 初始化
   * @param config 配置
   */
  public static initialize(config: TerrainGenerationWorkerManagerConfig = {}): void {
    if (this.initialized) {
      return;
    }

    // 检查是否支持Web Worker
    const supportsWorkers = typeof Worker !== 'undefined';
    this.enableMultithreading = supportsWorkers && (config.enableMultithreading !== false);

    // 如果支持多线程，创建工作线程管理器
    if (this.enableMultithreading) {
      try {
        this.workerManager = new TerrainGenerationWorkerManager(config);
        Debug.log('TerrainGenerationAlgorithms', '已初始化工作线程管理器');
      } catch (error) {
        Debug.error('TerrainGenerationAlgorithms', '初始化工作线程管理器失败:', error);
        this.enableMultithreading = false;
      }
    }

    this.initialized = true;
  }

  /**
   * 销毁
   */
  public static dispose(): void {
    if (this.workerManager) {
      (this.workerManager as any).dispose();
      this.workerManager = null;
    }
    this.initialized = false;
  }
  /**
   * 应用热侵蚀
   * @param terrain 地形组件
   * @param params 热侵蚀参数
   * @param useWorker 是否使用工作线程
   */
  public static async applyThermalErosion(
    terrain: TerrainComponent,
    params: ThermalErosionParams,
    useWorker: boolean = true
  ): Promise<void> {
    // 如果未初始化，先初始化
    if (!this.initialized) {
      this.initialize();
    }

    // 如果启用多线程并且有工作线程管理器，使用工作线程
    if (useWorker && this.enableMultithreading && this.workerManager) {
      try {
        // 准备参数
        const workerParams = {
          resolution: terrain.resolution,
          heightData: terrain.heightData,
          params: params
        };

        // 调用工作线程
        const result = await this.workerManager.applyErosion(workerParams);

        // 更新高度数据
        terrain.heightData.set(result.heightData);
        terrain.needsUpdate = true;
        return;
      } catch (error) {
        Debug.error('TerrainGenerationAlgorithms', '使用工作线程应用热侵蚀失败:', error);
        // 失败时回退到主线程
      }
    }

    // 在主线程中执行
    const { iterations, strength, slopeThreshold, depositionRate } = params;
    const resolution = terrain.resolution;
    const heightData = terrain.heightData;

    // 创建临时高度数据
    const tempHeightData = new Float32Array(heightData.length);

    // 执行多次迭代
    for (let iter = 0; iter < iterations; iter++) {
      // 复制当前高度数据
      heightData.forEach((height, i) => {
        tempHeightData[i] = height;
      });

      // 对每个点应用热侵蚀
      for (let z = 1; z < resolution - 1; z++) {
        for (let x = 1; x < resolution - 1; x++) {
          const index = z * resolution + x;
          const currentHeight = heightData[index];

          // 检查四个相邻点
          const neighbors = [
            { x: x, z: z - 1 },  // 上
            { x: x + 1, z: z },  // 右
            { x: x, z: z + 1 },  // 下
            { x: x - 1, z: z }   // 左
          ];

          // 计算坡度和沉积
          let totalDifference = 0;
          let maxDifference = 0;
          let maxDiffIndex = -1;

          for (let i = 0; i < neighbors.length; i++) {
            const nx = neighbors[i].x;
            const nz = neighbors[i].z;
            const nIndex = nz * resolution + nx;
            const neighborHeight = heightData[nIndex];
            const difference = currentHeight - neighborHeight;

            if (difference > slopeThreshold && difference > maxDifference) {
              maxDifference = difference;
              maxDiffIndex = nIndex;
            }
          }

          // 如果有超过阈值的坡度，应用侵蚀
          if (maxDiffIndex !== -1) {
            const erosionAmount = maxDifference * strength;
            tempHeightData[index] -= erosionAmount;
            tempHeightData[maxDiffIndex] += erosionAmount * depositionRate;
          }
        }
      }

      // 更新高度数据
      tempHeightData.forEach((height, i) => {
        heightData[i] = height;
      });
    }

    // 标记地形需要更新
    terrain.needsUpdate = true;
  }

  /**
   * 应用水侵蚀
   * @param terrain 地形组件
   * @param params 水侵蚀参数
   * @param useWorker 是否使用工作线程
   */
  public static async applyHydraulicErosion(
    terrain: TerrainComponent,
    params: HydraulicErosionParams,
    useWorker: boolean = true
  ): Promise<void> {
    // 如果未初始化，先初始化
    if (!this.initialized) {
      this.initialize();
    }

    // 如果启用多线程并且有工作线程管理器，使用工作线程
    if (useWorker && this.enableMultithreading && this.workerManager) {
      try {
        // 准备参数
        const workerParams = {
          resolution: terrain.resolution,
          heightData: terrain.heightData,
          params: params
        };

        // 调用工作线程
        const result = await this.workerManager.applyErosion(workerParams);

        // 更新高度数据
        terrain.heightData.set(result.heightData);
        terrain.needsUpdate = true;
        return;
      } catch (error) {
        Debug.error('TerrainGenerationAlgorithms', '使用工作线程应用水侵蚀失败:', error);
        // 失败时回退到主线程
      }
    }

    // 在主线程中执行
    const { iterations, droplets, capacity, erosionStrength, depositionStrength, evaporationRate, inertia, minSlope, gravity } = params;
    const resolution = terrain.resolution;
    const heightData = terrain.heightData;

    // 创建随机数生成器
    const random = () => Math.random();

    // 执行多次迭代
    for (let iter = 0; iter < iterations; iter++) {
      // 模拟多个雨滴
      for (let d = 0; d < droplets; d++) {
        // 随机位置
        let posX = random() * (resolution - 2) + 1;
        let posZ = random() * (resolution - 2) + 1;

        // 雨滴属性
        let dirX = 0;
        let dirZ = 0;
        let speed = 1;
        let water = 1;
        let sediment = 0;

        // 模拟雨滴流动
        while (water > 0) {
          // 计算当前位置的整数坐标
          const cellX = Math.floor(posX);
          const cellZ = Math.floor(posZ);
          const index = cellZ * resolution + cellX;

          // 计算梯度
          const gradientX = this.calculateGradient(heightData, resolution, cellX, cellZ, 1, 0);
          const gradientZ = this.calculateGradient(heightData, resolution, cellX, cellZ, 0, 1);

          // 更新方向
          dirX = dirX * inertia - gradientX * (1 - inertia);
          dirZ = dirZ * inertia - gradientZ * (1 - inertia);

          // 归一化方向
          const len = Math.sqrt(dirX * dirX + dirZ * dirZ);
          if (len > 0) {
            dirX /= len;
            dirZ /= len;
          }

          // 移动雨滴
          posX += dirX;
          posZ += dirZ;

          // 检查是否超出边界
          if (posX < 1 || posX >= resolution - 1 || posZ < 1 || posZ >= resolution - 1) {
            break;
          }

          // 计算新位置
          const newCellX = Math.floor(posX);
          const newCellZ = Math.floor(posZ);
          const newIndex = newCellZ * resolution + newCellX;

          // 计算高度差
          const heightDifference = heightData[index] - heightData[newIndex];

          // 计算沉积/侵蚀
          const sedimentCapacity = Math.max(-heightDifference, minSlope) * speed * water * capacity;

          if (sediment > sedimentCapacity) {
            // 沉积
            const depositAmount = (sediment - sedimentCapacity) * depositionStrength;
            heightData[index] += depositAmount;
            sediment -= depositAmount;
          } else {
            // 侵蚀
            const erosionAmount = Math.min((sedimentCapacity - sediment) * erosionStrength, -heightDifference);
            heightData[index] -= erosionAmount;
            sediment += erosionAmount;
          }

          // 更新速度和水量
          speed = Math.sqrt(speed * speed + heightDifference * gravity);
          water *= (1 - evaporationRate);
        }
      }
    }

    // 标记地形需要更新
    terrain.needsUpdate = true;
  }

  /**
   * 计算梯度
   * @param heightData 高度数据
   * @param resolution 分辨率
   * @param x X坐标
   * @param z Z坐标
   * @param dx X方向
   * @param dz Z方向
   * @returns 梯度
   */
  private static calculateGradient(heightData: Float32Array, resolution: number, x: number, z: number, dx: number, dz: number): number {
    const h1 = heightData[z * resolution + x];
    const h2 = heightData[(z + dz) * resolution + (x + dx)];
    return (h2 - h1);
  }

  /**
   * 生成河流
   * @param terrain 地形组件
   * @param params 河流生成参数
   * @param useWorker 是否使用工作线程
   */
  public static async generateRivers(
    terrain: TerrainComponent,
    params: RiverGenerationParams,
    useWorker: boolean = true
  ): Promise<void> {
    // 如果未初始化，先初始化
    if (!this.initialized) {
      this.initialize();
    }

    // 如果启用多线程并且有工作线程管理器，使用工作线程
    if (useWorker && this.enableMultithreading && this.workerManager) {
      try {
        // 准备参数
        const workerParams = {
          resolution: terrain.resolution,
          heightData: terrain.heightData,
          featureType: TerrainFeatureType.RIVER,
          params: params
        };

        // 调用工作线程
        const result = await this.workerManager.generateFeature(workerParams);

        // 更新高度数据
        terrain.heightData.set(result.heightData);
        terrain.needsUpdate = true;
        return;
      } catch (error) {
        Debug.error('TerrainGenerationAlgorithms', '使用工作线程生成河流失败:', error);
        // 失败时回退到主线程
      }
    }
    const { count, width, depth, sinuosity, branchProbability, minLength, maxLength, seed } = params;
    const resolution = terrain.resolution;
    const heightData = terrain.heightData;

    // 创建随机数生成器
    const random = this.createRandomGenerator(seed);

    // 创建河流路径数组
    const riverPaths: number[][] = [];

    // 生成多条河流
    for (let i = 0; i < count; i++) {
      // 随机选择起点（通常在较高的地方）
      let startX = Math.floor(random() * resolution);
      let startZ = Math.floor(random() * resolution);

      // 尝试找到较高的起点
      for (let attempt = 0; attempt < 10; attempt++) {
        const testX = Math.floor(random() * resolution);
        const testZ = Math.floor(random() * resolution);
        const testIndex = testZ * resolution + testX;
        const startIndex = startZ * resolution + startX;

        if (heightData[testIndex] > heightData[startIndex]) {
          startX = testX;
          startZ = testZ;
        }
      }

      // 创建河流路径
      const riverPath: number[] = [];
      riverPath.push(startZ * resolution + startX);

      // 当前位置
      let currentX = startX;
      let currentZ = startZ;

      // 河流长度
      const riverLength = Math.floor(minLength + random() * (maxLength - minLength));

      // 生成河流路径
      for (let step = 0; step < riverLength; step++) {
        // 计算周围8个方向的高度梯度
        const neighbors = [
          { dx: 0, dz: -1 },  // 上
          { dx: 1, dz: -1 },  // 右上
          { dx: 1, dz: 0 },   // 右
          { dx: 1, dz: 1 },   // 右下
          { dx: 0, dz: 1 },   // 下
          { dx: -1, dz: 1 },  // 左下
          { dx: -1, dz: 0 },  // 左
          { dx: -1, dz: -1 }  // 左上
        ];

        // 计算每个方向的权重（基于高度差和曲折度）
        const weights: number[] = [];
        let totalWeight = 0;

        for (let j = 0; j < neighbors.length; j++) {
          const nx = currentX + neighbors[j].dx;
          const nz = currentZ + neighbors[j].dz;

          // 检查边界
          if (nx < 0 || nx >= resolution || nz < 0 || nz >= resolution) {
            weights.push(0);
            continue;
          }

          const currentIndex = currentZ * resolution + currentX;
          const neighborIndex = nz * resolution + nx;

          // 计算高度差（负值表示向下流）
          const heightDiff = heightData[currentIndex] - heightData[neighborIndex];

          // 计算权重（高度差越大，权重越大）
          let weight = Math.max(0, heightDiff);

          // 添加曲折度（随机性）
          weight += sinuosity * random();

          // 存储权重
          weights.push(weight);
          totalWeight += weight;
        }

        // 如果没有可行的方向，结束河流
        if (totalWeight <= 0) {
          break;
        }

        // 根据权重随机选择方向
        let randomValue = random() * totalWeight;
        let selectedDirection = 0;

        for (let j = 0; j < weights.length; j++) {
          randomValue -= weights[j];
          if (randomValue <= 0) {
            selectedDirection = j;
            break;
          }
        }

        // 更新当前位置
        currentX += neighbors[selectedDirection].dx;
        currentZ += neighbors[selectedDirection].dz;

        // 检查边界
        if (currentX < 0 || currentX >= resolution || currentZ < 0 || currentZ >= resolution) {
          break;
        }

        // 添加到路径
        const index = currentZ * resolution + currentX;
        riverPath.push(index);

        // 随机生成分支
        if (random() < branchProbability && riverPath.length > 5) {
          // 创建分支
          const branchPath: number[] = [];
          let branchX = currentX;
          let branchZ = currentZ;

          // 分支长度
          const branchLength = Math.floor(riverLength * 0.5 * random());

          // 生成分支路径
          for (let branchStep = 0; branchStep < branchLength; branchStep++) {
            // 类似主河流的逻辑，但简化
            const branchDir = Math.floor(random() * neighbors.length);
            branchX += neighbors[branchDir].dx;
            branchZ += neighbors[branchDir].dz;

            // 检查边界
            if (branchX < 0 || branchX >= resolution || branchZ < 0 || branchZ >= resolution) {
              break;
            }

            // 添加到分支路径
            const branchIndex = branchZ * resolution + branchX;
            branchPath.push(branchIndex);
          }

          // 添加分支到河流路径数组
          if (branchPath.length > 3) {
            riverPaths.push(branchPath);
          }
        }
      }

      // 添加河流路径到数组
      if (riverPath.length > 3) {
        riverPaths.push(riverPath);
      }
    }

    // 应用河流到地形
    this.applyRiversToTerrain(terrain, riverPaths, width, depth);

    // 标记地形需要更新
    terrain.needsUpdate = true;
  }

  /**
   * 应用河流到地形
   * @param terrain 地形组件
   * @param riverPaths 河流路径
   * @param width 河流宽度
   * @param depth 河流深度
   */
  private static applyRiversToTerrain(terrain: TerrainComponent, riverPaths: number[][], width: number, depth: number): void {
    const resolution = terrain.resolution;
    const heightData = terrain.heightData;

    // 对每条河流路径
    for (const riverPath of riverPaths) {
      // 对路径上的每个点
      for (let i = 0; i < riverPath.length; i++) {
        const index = riverPath[i];
        const x = index % resolution;
        const z = Math.floor(index / resolution);

        // 计算河流宽度（可以沿路径变化）
        const riverWidth = width * (0.5 + 0.5 * i / riverPath.length);

        // 应用河流深度
        for (let dz = -Math.ceil(riverWidth); dz <= Math.ceil(riverWidth); dz++) {
          for (let dx = -Math.ceil(riverWidth); dx <= Math.ceil(riverWidth); dx++) {
            const nx = x + dx;
            const nz = z + dz;

            // 检查边界
            if (nx < 0 || nx >= resolution || nz < 0 || nz >= resolution) {
              continue;
            }

            // 计算到河流中心的距离
            const distance = Math.sqrt(dx * dx + dz * dz);

            // 如果在河流宽度内
            if (distance <= riverWidth) {
              const nIndex = nz * resolution + nx;

              // 计算深度因子（边缘较浅）
              const depthFactor = 1 - (distance / riverWidth);

              // 应用河流深度
              heightData[nIndex] -= depth * depthFactor;

              // 确保高度不会低于0
              heightData[nIndex] = Math.max(0, heightData[nIndex]);
            }
          }
        }
      }
    }
  }

  /**
   * 生成山脉
   * @param terrain 地形组件
   * @param params 山脉生成参数
   * @param useWorker 是否使用工作线程
   */
  public static async generateMountains(
    terrain: TerrainComponent,
    params: MountainGenerationParams,
    useWorker: boolean = true
  ): Promise<void> {
    // 如果未初始化，先初始化
    if (!this.initialized) {
      this.initialize();
    }

    // 如果启用多线程并且有工作线程管理器，使用工作线程
    if (useWorker && this.enableMultithreading && this.workerManager) {
      try {
        // 准备参数
        const workerParams = {
          resolution: terrain.resolution,
          heightData: terrain.heightData,
          featureType: TerrainFeatureType.MOUNTAIN,
          params: params
        };

        // 调用工作线程
        const result = await this.workerManager.generateFeature(workerParams);

        // 更新高度数据
        terrain.heightData.set(result.heightData);
        terrain.needsUpdate = true;
        return;
      } catch (error) {
        Debug.error('TerrainGenerationAlgorithms', '使用工作线程生成山脉失败:', error);
        // 失败时回退到主线程
      }
    }
    const { count, height, width, roughness, sharpness, seed } = params;
    const resolution = terrain.resolution;
    const heightData = terrain.heightData;

    // 创建随机数生成器
    const random = this.createRandomGenerator(seed);

    // 生成多个山脉
    for (let i = 0; i < count; i++) {
      // 随机选择山脉起点
      const startX = Math.floor(random() * resolution);
      const startZ = Math.floor(random() * resolution);

      // 山脉长度
      const mountainLength = Math.floor(resolution * 0.2 + random() * resolution * 0.6);

      // 山脉方向
      const dirX = Math.cos(random() * Math.PI * 2);
      const dirZ = Math.sin(random() * Math.PI * 2);

      // 生成山脉路径
      const mountainPath: { x: number, z: number }[] = [];
      let currentX = startX;
      let currentZ = startZ;

      for (let step = 0; step < mountainLength; step++) {
        // 添加当前点到路径
        mountainPath.push({ x: Math.floor(currentX), z: Math.floor(currentZ) });

        // 添加随机性到方向
        const randomAngle = (random() - 0.5) * roughness;
        const newDirX = dirX * Math.cos(randomAngle) - dirZ * Math.sin(randomAngle);
        const newDirZ = dirX * Math.sin(randomAngle) + dirZ * Math.cos(randomAngle);

        // 更新位置
        currentX += newDirX;
        currentZ += newDirZ;

        // 检查边界
        if (currentX < 0 || currentX >= resolution || currentZ < 0 || currentZ >= resolution) {
          break;
        }
      }

      // 应用山脉到地形
      this.applyMountainToTerrain(terrain, mountainPath, height, width, sharpness);
    }

    // 标记地形需要更新
    terrain.needsUpdate = true;
  }

  /**
   * 应用山脉到地形
   * @param terrain 地形组件
   * @param mountainPath 山脉路径
   * @param height 山脉高度
   * @param width 山脉宽度
   * @param sharpness 山脉锐度
   */
  private static applyMountainToTerrain(
    terrain: TerrainComponent,
    mountainPath: { x: number, z: number }[],
    height: number,
    width: number,
    sharpness: number
  ): void {
    const resolution = terrain.resolution;
    const heightData = terrain.heightData;

    // 对路径上的每个点
    for (let i = 0; i < mountainPath.length; i++) {
      const { x, z } = mountainPath[i];

      // 计算山脉高度（可以沿路径变化）
      const mountainHeight = height * (0.5 + 0.5 * Math.sin(Math.PI * i / mountainPath.length));

      // 应用山脉高度
      for (let dz = -Math.ceil(width); dz <= Math.ceil(width); dz++) {
        for (let dx = -Math.ceil(width); dx <= Math.ceil(width); dx++) {
          const nx = x + dx;
          const nz = z + dz;

          // 检查边界
          if (nx < 0 || nx >= resolution || nz < 0 || nz >= resolution) {
            continue;
          }

          // 计算到山脉中心的距离
          const distance = Math.sqrt(dx * dx + dz * dz);

          // 如果在山脉宽度内
          if (distance <= width) {
            const nIndex = nz * resolution + nx;

            // 计算高度因子（基于距离和锐度）
            const heightFactor = Math.pow(1 - (distance / width), sharpness);

            // 应用山脉高度
            heightData[nIndex] += mountainHeight * heightFactor;

            // 确保高度不会超过1
            heightData[nIndex] = Math.min(1, heightData[nIndex]);
          }
        }
      }
    }
  }

  /**
   * 生成峡谷
   * @param terrain 地形组件
   * @param params 峡谷生成参数
   * @param useWorker 是否使用工作线程
   */
  public static async generateCanyons(
    terrain: TerrainComponent,
    params: CanyonGenerationParams,
    useWorker: boolean = true
  ): Promise<void> {
    // 如果未初始化，先初始化
    if (!this.initialized) {
      this.initialize();
    }

    // 如果启用多线程并且有工作线程管理器，使用工作线程
    if (useWorker && this.enableMultithreading && this.workerManager) {
      try {
        // 准备参数
        const workerParams = {
          resolution: terrain.resolution,
          heightData: terrain.heightData,
          featureType: TerrainFeatureType.CANYON,
          params: params
        };

        // 调用工作线程
        const result = await this.workerManager.generateFeature(workerParams);

        // 更新高度数据
        terrain.heightData.set(result.heightData);
        terrain.needsUpdate = true;
        return;
      } catch (error) {
        Debug.error('TerrainGenerationAlgorithms', '使用工作线程生成峡谷失败:', error);
        // 失败时回退到主线程
      }
    }
    const { count, depth, width, sinuosity, roughness, seed } = params;
    const resolution = terrain.resolution;
    const heightData = terrain.heightData;

    // 创建随机数生成器
    const random = this.createRandomGenerator(seed);

    // 生成多个峡谷
    for (let i = 0; i < count; i++) {
      // 随机选择峡谷起点
      const startX = Math.floor(random() * resolution);
      const startZ = Math.floor(random() * resolution);

      // 峡谷长度
      const canyonLength = Math.floor(resolution * 0.2 + random() * resolution * 0.6);

      // 峡谷方向
      const dirX = Math.cos(random() * Math.PI * 2);
      const dirZ = Math.sin(random() * Math.PI * 2);

      // 生成峡谷路径
      const canyonPath: { x: number, z: number }[] = [];
      let currentX = startX;
      let currentZ = startZ;

      for (let step = 0; step < canyonLength; step++) {
        // 添加当前点到路径
        canyonPath.push({ x: Math.floor(currentX), z: Math.floor(currentZ) });

        // 添加随机性到方向（曲折度）
        const randomAngle = (random() - 0.5) * sinuosity;
        const newDirX = dirX * Math.cos(randomAngle) - dirZ * Math.sin(randomAngle);
        const newDirZ = dirX * Math.sin(randomAngle) + dirZ * Math.cos(randomAngle);

        // 更新位置
        currentX += newDirX;
        currentZ += newDirZ;

        // 检查边界
        if (currentX < 0 || currentX >= resolution || currentZ < 0 || currentZ >= resolution) {
          break;
        }
      }

      // 应用峡谷到地形
      this.applyCanyonToTerrain(terrain, canyonPath, depth, width, roughness);
    }

    // 标记地形需要更新
    terrain.needsUpdate = true;
  }

  /**
   * 应用峡谷到地形
   * @param terrain 地形组件
   * @param canyonPath 峡谷路径
   * @param depth 峡谷深度
   * @param width 峡谷宽度
   * @param roughness 峡谷粗糙度
   */
  private static applyCanyonToTerrain(
    terrain: TerrainComponent,
    canyonPath: { x: number, z: number }[],
    depth: number,
    width: number,
    roughness: number
  ): void {
    const resolution = terrain.resolution;
    const heightData = terrain.heightData;

    // 对路径上的每个点
    for (let i = 0; i < canyonPath.length; i++) {
      const { x, z } = canyonPath[i];

      // 计算峡谷深度（可以沿路径变化）
      const canyonDepth = depth * (0.5 + 0.5 * Math.sin(Math.PI * i / canyonPath.length));

      // 应用峡谷深度
      for (let dz = -Math.ceil(width); dz <= Math.ceil(width); dz++) {
        for (let dx = -Math.ceil(width); dx <= Math.ceil(width); dx++) {
          const nx = x + dx;
          const nz = z + dz;

          // 检查边界
          if (nx < 0 || nx >= resolution || nz < 0 || nz >= resolution) {
            continue;
          }

          // 计算到峡谷中心的距离
          const distance = Math.sqrt(dx * dx + dz * dz);

          // 如果在峡谷宽度内
          if (distance <= width) {
            const nIndex = nz * resolution + nx;

            // 计算深度因子（基于距离和粗糙度）
            let depthFactor = Math.pow(1 - (distance / width), 2);

            // 添加粗糙度
            if (roughness > 0) {
              const noiseValue = this.simplexNoise(nx * 0.1, nz * 0.1, seed + i) * roughness;
              depthFactor *= (1 + noiseValue);
            }

            // 应用峡谷深度
            heightData[nIndex] -= canyonDepth * depthFactor;

            // 确保高度不会低于0
            heightData[nIndex] = Math.max(0, heightData[nIndex]);
          }
        }
      }
    }
  }

  /**
   * 创建随机数生成器
   * @param seed 种子
   * @returns 随机数生成器函数
   */
  private static createRandomGenerator(seed: number): () => number {
    // 简单的伪随机数生成器
    let s = seed;
    return () => {
      s = (s * 9301 + 49297) % 233280;
      return s / 233280;
    };
  }

  /**
   * 生成洞穴
   * @param terrain 地形组件
   * @param params 洞穴生成参数
   * @param useWorker 是否使用工作线程
   */
  public static async generateCaves(
    terrain: TerrainComponent,
    params: CaveGenerationParams,
    useWorker: boolean = true
  ): Promise<void> {
    // 如果未初始化，先初始化
    if (!this.initialized) {
      this.initialize();
    }

    // 如果启用多线程并且有工作线程管理器，使用工作线程
    if (useWorker && this.enableMultithreading && this.workerManager) {
      try {
        // 准备参数
        const workerParams = {
          resolution: terrain.resolution,
          heightData: terrain.heightData,
          featureType: TerrainFeatureType.CAVE,
          params: params
        };

        // 调用工作线程
        const result = await this.workerManager.generateFeature(workerParams);

        // 更新高度数据
        terrain.heightData.set(result.heightData);
        terrain.needsUpdate = true;
        return;
      } catch (error) {
        Debug.error('TerrainGenerationAlgorithms', '使用工作线程生成洞穴失败:', error);
        // 失败时回退到主线程
      }
    }
    const { count, size, depth, complexity, connectionProbability, seed } = params;
    const resolution = terrain.resolution;
    const heightData = terrain.heightData;

    // 创建随机数生成器
    const random = this.createRandomGenerator(seed);

    // 存储洞穴中心点
    const caveCenters: { x: number, z: number, radius: number }[] = [];

    // 生成多个洞穴
    for (let i = 0; i < count; i++) {
      // 随机选择洞穴中心
      const centerX = Math.floor(random() * resolution);
      const centerZ = Math.floor(random() * resolution);

      // 随机洞穴大小（半径）
      const radius = size * (0.5 + random() * 0.5);

      // 存储洞穴中心
      caveCenters.push({ x: centerX, z: centerZ, radius });

      // 应用洞穴到地形
      this.applyCaveToTerrain(terrain, centerX, centerZ, radius, depth, complexity, random);
    }

    // 连接洞穴
    if (connectionProbability > 0 && caveCenters.length > 1) {
      for (let i = 0; i < caveCenters.length; i++) {
        for (let j = i + 1; j < caveCenters.length; j++) {
          // 根据连接概率决定是否连接
          if (random() < connectionProbability) {
            this.connectCaves(terrain, caveCenters[i], caveCenters[j], depth * 0.8, complexity * 0.5, random);
          }
        }
      }
    }

    // 标记地形需要更新
    terrain.needsUpdate = true;
  }

  /**
   * 应用洞穴到地形
   * @param terrain 地形组件
   * @param centerX 中心X坐标
   * @param centerZ 中心Z坐标
   * @param radius 半径
   * @param depth 深度
   * @param complexity 复杂度
   * @param random 随机数生成器
   */
  private static applyCaveToTerrain(
    terrain: TerrainComponent,
    centerX: number,
    centerZ: number,
    radius: number,
    depth: number,
    complexity: number,
    random: () => number
  ): void {
    const resolution = terrain.resolution;
    const heightData = terrain.heightData;

    // 计算洞穴范围
    const minX = Math.max(0, Math.floor(centerX - radius));
    const maxX = Math.min(resolution - 1, Math.ceil(centerX + radius));
    const minZ = Math.max(0, Math.floor(centerZ - radius));
    const maxZ = Math.min(resolution - 1, Math.ceil(centerZ + radius));

    // 对范围内的每个点应用洞穴深度
    for (let z = minZ; z <= maxZ; z++) {
      for (let x = minX; x <= maxX; x++) {
        // 计算到洞穴中心的距离
        const dx = x - centerX;
        const dz = z - centerZ;
        const distance = Math.sqrt(dx * dx + dz * dz);

        // 如果在洞穴半径内
        if (distance <= radius) {
          const index = z * resolution + x;

          // 计算深度因子（基于距离和复杂度）
          let depthFactor = Math.pow(1 - (distance / radius), 2);

          // 添加复杂度（噪声）
          if (complexity > 0) {
            const noiseValue = this.simplexNoise(x * 0.1, z * 0.1, Math.floor(random() * 1000)) * complexity;
            depthFactor *= (1 + noiseValue);
          }

          // 应用洞穴深度
          heightData[index] -= depth * depthFactor;

          // 确保高度不会低于0
          heightData[index] = Math.max(0, heightData[index]);
        }
      }
    }
  }

  /**
   * 连接两个洞穴
   * @param terrain 地形组件
   * @param cave1 第一个洞穴
   * @param cave2 第二个洞穴
   * @param depth 深度
   * @param complexity 复杂度
   * @param random 随机数生成器
   */
  private static connectCaves(
    terrain: TerrainComponent,
    cave1: { x: number, z: number, radius: number },
    cave2: { x: number, z: number, radius: number },
    depth: number,
    complexity: number,
    random: () => number
  ): void {
    const resolution = terrain.resolution;
    const heightData = terrain.heightData;

    // 计算两个洞穴之间的距离和方向
    const dx = cave2.x - cave1.x;
    const dz = cave2.z - cave1.z;
    const distance = Math.sqrt(dx * dx + dz * dz);

    // 归一化方向
    const dirX = dx / distance;
    const dirZ = dz / distance;

    // 通道宽度（两个洞穴半径的平均值的一半）
    const tunnelWidth = (cave1.radius + cave2.radius) / 4;

    // 创建连接通道
    const steps = Math.ceil(distance);
    let currentX = cave1.x;
    let currentZ = cave1.z;

    for (let step = 0; step < steps; step++) {
      // 添加一些随机性到路径
      const randomAngle = (random() - 0.5) * complexity * 0.5;
      const newDirX = dirX * Math.cos(randomAngle) - dirZ * Math.sin(randomAngle);
      const newDirZ = dirX * Math.sin(randomAngle) + dirZ * Math.cos(randomAngle);

      // 更新位置
      currentX += newDirX;
      currentZ += newDirZ;

      // 确保在地形范围内
      if (currentX < 0 || currentX >= resolution || currentZ < 0 || currentZ >= resolution) {
        continue;
      }

      // 在当前位置创建一个小洞穴
      const x = Math.floor(currentX);
      const z = Math.floor(currentZ);

      // 计算通道范围
      const minX = Math.max(0, Math.floor(x - tunnelWidth));
      const maxX = Math.min(resolution - 1, Math.ceil(x + tunnelWidth));
      const minZ = Math.max(0, Math.floor(z - tunnelWidth));
      const maxZ = Math.min(resolution - 1, Math.ceil(z + tunnelWidth));

      // 对范围内的每个点应用通道深度
      for (let tz = minZ; tz <= maxZ; tz++) {
        for (let tx = minX; tx <= maxX; tx++) {
          // 计算到通道中心的距离
          const tdx = tx - x;
          const tdz = tz - z;
          const tDistance = Math.sqrt(tdx * tdx + tdz * tdz);

          // 如果在通道宽度内
          if (tDistance <= tunnelWidth) {
            const index = tz * resolution + tx;

            // 计算深度因子
            let depthFactor = Math.pow(1 - (tDistance / tunnelWidth), 2);

            // 应用通道深度
            heightData[index] -= depth * depthFactor;

            // 确保高度不会低于0
            heightData[index] = Math.max(0, heightData[index]);
          }
        }
      }
    }
  }

  /**
   * 生成悬崖
   * @param terrain 地形组件
   * @param params 悬崖生成参数
   * @param useWorker 是否使用工作线程
   */
  public static async generateCliffs(
    terrain: TerrainComponent,
    params: CliffGenerationParams,
    useWorker: boolean = true
  ): Promise<void> {
    // 如果未初始化，先初始化
    if (!this.initialized) {
      this.initialize();
    }

    // 如果启用多线程并且有工作线程管理器，使用工作线程
    if (useWorker && this.enableMultithreading && this.workerManager) {
      try {
        // 准备参数
        const workerParams = {
          resolution: terrain.resolution,
          heightData: terrain.heightData,
          featureType: TerrainFeatureType.CLIFF,
          params: params
        };

        // 调用工作线程
        const result = await this.workerManager.generateFeature(workerParams);

        // 更新高度数据
        terrain.heightData.set(result.heightData);
        terrain.needsUpdate = true;
        return;
      } catch (error) {
        Debug.error('TerrainGenerationAlgorithms', '使用工作线程生成悬崖失败:', error);
        // 失败时回退到主线程
      }
    }
    const { count, height, width, steepness, roughness, seed } = params;
    const resolution = terrain.resolution;
    const heightData = terrain.heightData;

    // 创建随机数生成器
    const random = this.createRandomGenerator(seed);

    // 生成多个悬崖
    for (let i = 0; i < count; i++) {
      // 随机选择悬崖起点
      const startX = Math.floor(random() * resolution);
      const startZ = Math.floor(random() * resolution);

      // 悬崖长度
      const cliffLength = Math.floor(resolution * 0.1 + random() * resolution * 0.3);

      // 悬崖方向
      const dirX = Math.cos(random() * Math.PI * 2);
      const dirZ = Math.sin(random() * Math.PI * 2);

      // 生成悬崖路径
      const cliffPath: { x: number, z: number }[] = [];
      let currentX = startX;
      let currentZ = startZ;

      for (let step = 0; step < cliffLength; step++) {
        // 添加当前点到路径
        cliffPath.push({ x: Math.floor(currentX), z: Math.floor(currentZ) });

        // 添加随机性到方向
        const randomAngle = (random() - 0.5) * roughness * 0.5;
        const newDirX = dirX * Math.cos(randomAngle) - dirZ * Math.sin(randomAngle);
        const newDirZ = dirX * Math.sin(randomAngle) + dirZ * Math.cos(randomAngle);

        // 更新位置
        currentX += newDirX;
        currentZ += newDirZ;

        // 检查边界
        if (currentX < 0 || currentX >= resolution || currentZ < 0 || currentZ >= resolution) {
          break;
        }
      }

      // 应用悬崖到地形
      this.applyCliffToTerrain(terrain, cliffPath, height, width, steepness, roughness, seed);
    }

    // 标记地形需要更新
    terrain.needsUpdate = true;
  }

  /**
   * 应用悬崖到地形
   * @param terrain 地形组件
   * @param cliffPath 悬崖路径
   * @param height 悬崖高度
   * @param width 悬崖宽度
   * @param steepness 悬崖陡峭度
   * @param roughness 悬崖粗糙度
   * @param seed 种子
   */
  private static applyCliffToTerrain(
    terrain: TerrainComponent,
    cliffPath: { x: number, z: number }[],
    height: number,
    width: number,
    steepness: number,
    roughness: number,
    seed: number
  ): void {
    const resolution = terrain.resolution;
    const heightData = terrain.heightData;

    // 对路径上的每个点
    for (let i = 0; i < cliffPath.length; i++) {
      const { x, z } = cliffPath[i];

      // 计算悬崖高度（可以沿路径变化）
      const cliffHeight = height * (0.8 + 0.4 * Math.sin(Math.PI * i / cliffPath.length));

      // 计算悬崖方向（垂直于路径）
      let dirX = 0;
      let dirZ = 0;

      if (i > 0 && i < cliffPath.length - 1) {
        // 计算路径方向
        const prevX = cliffPath[i - 1].x;
        const prevZ = cliffPath[i - 1].z;
        const nextX = cliffPath[i + 1].x;
        const nextZ = cliffPath[i + 1].z;

        // 路径方向
        const pathDirX = nextX - prevX;
        const pathDirZ = nextZ - prevZ;

        // 垂直方向（顺时针旋转90度）
        dirX = -pathDirZ;
        dirZ = pathDirX;

        // 归一化
        const len = Math.sqrt(dirX * dirX + dirZ * dirZ);
        if (len > 0) {
          dirX /= len;
          dirZ /= len;
        }
      } else {
        // 对于起点和终点，使用默认方向
        dirX = 1;
        dirZ = 0;
      }

      // 应用悬崖高度
      for (let d = -Math.ceil(width); d <= Math.ceil(width); d++) {
        // 计算当前点
        const nx = Math.floor(x + d * dirX);
        const nz = Math.floor(z + d * dirZ);

        // 检查边界
        if (nx < 0 || nx >= resolution || nz < 0 || nz >= resolution) {
          continue;
        }

        // 计算到悬崖中心的距离
        const distance = Math.abs(d);

        // 如果在悬崖宽度内
        if (distance <= width) {
          const nIndex = nz * resolution + nx;

          // 计算高度因子
          let heightFactor = 0;

          // 悬崖一侧升高，另一侧保持不变
          if (d > 0) {
            // 陡峭的悬崖边缘
            if (distance < width * 0.2) {
              heightFactor = 1 - Math.pow(distance / (width * 0.2), steepness);
            } else {
              heightFactor = 0;
            }
          } else {
            // 缓慢上升的一侧
            heightFactor = Math.pow(1 - Math.abs(d) / width, 1 / steepness);
          }

          // 添加粗糙度
          if (roughness > 0) {
            const noiseValue = this.simplexNoise(nx * 0.1, nz * 0.1, seed + i) * roughness;
            heightFactor *= (1 + noiseValue * 0.3);
          }

          // 应用悬崖高度
          heightData[nIndex] += cliffHeight * heightFactor;

          // 确保高度不会超过1
          heightData[nIndex] = Math.min(1, heightData[nIndex]);
        }
      }
    }
  }

  /**
   * 生成火山
   * @param terrain 地形组件
   * @param params 火山生成参数
   * @param useWorker 是否使用工作线程
   */
  public static async generateVolcanoes(
    terrain: TerrainComponent,
    params: VolcanoGenerationParams,
    useWorker: boolean = true
  ): Promise<void> {
    // 如果未初始化，先初始化
    if (!this.initialized) {
      this.initialize();
    }

    // 如果启用多线程并且有工作线程管理器，使用工作线程
    if (useWorker && this.enableMultithreading && this.workerManager) {
      try {
        // 准备参数
        const workerParams = {
          resolution: terrain.resolution,
          heightData: terrain.heightData,
          featureType: TerrainFeatureType.VOLCANO,
          params: params
        };

        // 调用工作线程
        const result = await this.workerManager.generateFeature(workerParams);

        // 更新高度数据
        terrain.heightData.set(result.heightData);
        terrain.needsUpdate = true;
        return;
      } catch (error) {
        Debug.error('TerrainGenerationAlgorithms', '使用工作线程生成火山失败:', error);
        // 失败时回退到主线程
      }
    }
    const { count, height, radius, craterSize, craterDepth, seed } = params;
    const resolution = terrain.resolution;
    const heightData = terrain.heightData;

    // 创建随机数生成器
    const random = this.createRandomGenerator(seed);

    // 生成多个火山
    for (let i = 0; i < count; i++) {
      // 随机选择火山中心
      const centerX = Math.floor(random() * resolution);
      const centerZ = Math.floor(random() * resolution);

      // 计算火山范围
      const minX = Math.max(0, Math.floor(centerX - radius));
      const maxX = Math.min(resolution - 1, Math.ceil(centerX + radius));
      const minZ = Math.max(0, Math.floor(centerZ - radius));
      const maxZ = Math.min(resolution - 1, Math.ceil(centerZ + radius));

      // 对范围内的每个点应用火山高度
      for (let z = minZ; z <= maxZ; z++) {
        for (let x = minX; x <= maxX; x++) {
          // 计算到火山中心的距离
          const dx = x - centerX;
          const dz = z - centerZ;
          const distance = Math.sqrt(dx * dx + dz * dz);

          // 如果在火山半径内
          if (distance <= radius) {
            const index = z * resolution + x;

            // 计算高度因子（基于距离）
            let heightFactor = 0;

            // 火山形状：边缘缓慢上升，中心陡峭
            if (distance > radius * 0.8) {
              // 火山边缘
              heightFactor = 1 - (distance - radius * 0.8) / (radius * 0.2);
            } else if (distance > radius * 0.2) {
              // 火山斜坡
              heightFactor = 0.2 + 0.8 * (1 - (distance - radius * 0.2) / (radius * 0.6));
            } else {
              // 火山顶部
              heightFactor = 1.0;
            }

            // 应用火山高度
            heightData[index] += height * heightFactor;

            // 如果在火山口范围内，应用火山口深度
            if (distance < craterSize) {
              // 火山口深度因子
              const craterFactor = 1 - distance / craterSize;
              heightData[index] -= craterDepth * craterFactor;
            }

            // 确保高度在有效范围内
            heightData[index] = Math.min(1, Math.max(0, heightData[index]));
          }
        }
      }
    }

    // 标记地形需要更新
    terrain.needsUpdate = true;
  }

  /**
   * 生成地形特征组合
   * @param terrain 地形组件
   * @param params 地形特征组合参数
   * @param useWorker 是否使用工作线程
   */
  public static async generateTerrainFeatureCombination(
    terrain: TerrainComponent,
    params: TerrainFeatureCombinationParams,
    useWorker: boolean = true
  ): Promise<void> {
    // 如果未初始化，先初始化
    if (!this.initialized) {
      this.initialize();
    }

    // 如果启用多线程并且有工作线程管理器，使用工作线程
    if (useWorker && this.enableMultithreading && this.workerManager) {
      try {
        // 准备参数
        const workerParams = {
          resolution: terrain.resolution,
          params: params
        };

        // 调用工作线程
        const result = await this.workerManager.generateFeatureCombination(workerParams);

        // 更新高度数据
        terrain.heightData.set(result.heightData);
        terrain.needsUpdate = true;
        return;
      } catch (error) {
        Debug.error('TerrainGenerationAlgorithms', '使用工作线程生成地形特征组合失败:', error);
        // 失败时回退到主线程
      }
    }

    // 在主线程中执行
    const { baseTerrainType, baseTerrainParams, features, seed } = params;

    // 创建随机数生成器
    const random = this.createRandomGenerator(seed);

    // 首先生成基础地形
    this.generateBaseTerrain(terrain, baseTerrainType, baseTerrainParams);

    // 然后按照权重应用特征
    for (const feature of features) {
      // 根据权重决定是否应用该特征
      if (random() <= feature.weight) {
        await this.applyTerrainFeature(terrain, feature.type, feature.params, useWorker);
      }
    }

    // 标记地形需要更新
    terrain.needsUpdate = true;
  }

  /**
   * 生成基础地形
   * @param terrain 地形组件
   * @param terrainType 地形类型
   * @param params 参数
   */
  private static async generateBaseTerrain(terrain: TerrainComponent, terrainType: TerrainFeatureType, params: any): Promise<void> {
    switch (terrainType) {
      case TerrainFeatureType.MOUNTAIN:
        await this.generateMountains(terrain, params);
        break;
      case TerrainFeatureType.CANYON:
        await this.generateCanyons(terrain, params);
        break;
      case TerrainFeatureType.RIVER:
        // 对于河流，先生成基础地形，再添加河流
        this.generateFlatTerrain(terrain, 0.2);
        await this.generateRivers(terrain, params);
        break;
      case TerrainFeatureType.CAVE:
        // 对于洞穴，先生成基础地形，再添加洞穴
        this.generateFlatTerrain(terrain, 0.5);
        await this.generateCaves(terrain, params);
        break;
      case TerrainFeatureType.CLIFF:
        await this.generateCliffs(terrain, params);
        break;
      case TerrainFeatureType.PLAIN:
        this.generateFlatTerrain(terrain, 0.1);
        break;
      case TerrainFeatureType.HILLS:
        this.generateHillyTerrain(terrain, params || { scale: 80, persistence: 0.4, octaves: 5, frequency: 0.008, amplitude: 0.8 });
        break;
      case TerrainFeatureType.DESERT:
        this.generateDesertTerrain(terrain, params || { scale: 100, dunes: 20, duneHeight: 0.3, duneWidth: 30 });
        break;
      case TerrainFeatureType.VOLCANO:
        await this.generateVolcanoes(terrain, params);
        break;
      case TerrainFeatureType.ISLAND:
        this.generateIslandTerrain(terrain, params || { radius: terrain.resolution * 0.4, height: 0.8, falloff: 0.7 });
        break;
      case TerrainFeatureType.UNDERGROUND_RIVER:
        // 对于地下河，先生成基础地形，再添加地下河
        this.generateFlatTerrain(terrain, 0.3);
        this.generateUndergroundRivers(terrain, params);
        break;
      case TerrainFeatureType.UNDERGROUND_LAKE:
        // 对于地下湖泊，先生成基础地形，再添加地下湖泊
        this.generateFlatTerrain(terrain, 0.3);
        this.generateUndergroundLakes(terrain, params);
        break;
      default:
        // 默认生成平坦地形
        this.generateFlatTerrain(terrain, 0.2);
        break;
    }
  }

  /**
   * 应用地形特征
   * @param terrain 地形组件
   * @param featureType 特征类型
   * @param params 参数
   * @param useWorker 是否使用工作线程
   */
  private static async applyTerrainFeature(
    terrain: TerrainComponent,
    featureType: TerrainFeatureType,
    params: any,
    useWorker: boolean = true
  ): Promise<void> {
    // 如果启用多线程并且有工作线程管理器，使用工作线程
    if (useWorker && this.enableMultithreading && this.workerManager) {
      try {
        // 准备参数
        const workerParams = {
          resolution: terrain.resolution,
          heightData: terrain.heightData,
          featureType: featureType,
          params: params
        };

        // 调用工作线程
        const result = await this.workerManager.generateFeature(workerParams);

        // 更新高度数据
        terrain.heightData.set(result.heightData);
        terrain.needsUpdate = true;
        return;
      } catch (error) {
        Debug.error('TerrainGenerationAlgorithms', `使用工作线程应用地形特征 ${featureType} 失败:`, error);
        // 失败时回退到主线程
      }
    }

    // 在主线程中执行
    switch (featureType) {
      case TerrainFeatureType.MOUNTAIN:
        await this.generateMountains(terrain, params, false);
        break;
      case TerrainFeatureType.CANYON:
        await this.generateCanyons(terrain, params, false);
        break;
      case TerrainFeatureType.RIVER:
        await this.generateRivers(terrain, params, false);
        break;
      case TerrainFeatureType.CAVE:
        await this.generateCaves(terrain, params, false);
        break;
      case TerrainFeatureType.CLIFF:
        await this.generateCliffs(terrain, params, false);
        break;
      case TerrainFeatureType.VOLCANO:
        await this.generateVolcanoes(terrain, params, false);
        break;
      case TerrainFeatureType.UNDERGROUND_RIVER:
        this.generateUndergroundRivers(terrain, params);
        break;
      case TerrainFeatureType.UNDERGROUND_LAKE:
        this.generateUndergroundLakes(terrain, params);
        break;
      default:
        // 不支持的特征类型
        Debug.warn(`不支持的地形特征类型: ${featureType}`);
        break;
    }
  }

  /**
   * 生成平坦地形
   * @param terrain 地形组件
   * @param baseHeight 基础高度
   */
  private static generateFlatTerrain(terrain: TerrainComponent, baseHeight: number): void {
    const resolution = terrain.resolution;
    const heightData = terrain.heightData;

    // 设置所有点的高度为基础高度
    for (let i = 0; i < heightData.length; i++) {
      heightData[i] = baseHeight;
    }

    // 标记地形需要更新
    terrain.needsUpdate = true;
  }

  /**
   * 生成丘陵地形
   * @param terrain 地形组件
   * @param params 参数
   */
  private static generateHillyTerrain(terrain: TerrainComponent, params: any): void {
    const { scale, persistence, octaves, frequency, amplitude } = params;
    const resolution = terrain.resolution;
    const heightData = terrain.heightData;
    const seed = params.seed || Math.floor(Math.random() * 1000);

    // 使用柏林噪声生成丘陵
    for (let z = 0; z < resolution; z++) {
      for (let x = 0; x < resolution; x++) {
        const index = z * resolution + x;

        // 计算噪声值
        let noise = 0;
        let amp = amplitude;
        let freq = frequency;

        for (let o = 0; o < octaves; o++) {
          const noiseValue = this.simplexNoise(x * freq, z * freq, seed + o);
          noise += noiseValue * amp;
          amp *= persistence;
          freq *= 2;
        }

        // 应用噪声值
        heightData[index] = Math.min(1, Math.max(0, noise));
      }
    }

    // 标记地形需要更新
    terrain.needsUpdate = true;
  }

  /**
   * 生成沙漠地形
   * @param terrain 地形组件
   * @param params 参数
   */
  private static generateDesertTerrain(terrain: TerrainComponent, params: any): void {
    const { scale, dunes, duneHeight, duneWidth } = params;
    const resolution = terrain.resolution;
    const heightData = terrain.heightData;
    const seed = params.seed || Math.floor(Math.random() * 1000);

    // 首先生成平坦的基础地形
    this.generateFlatTerrain(terrain, 0.1);

    // 创建随机数生成器
    const random = this.createRandomGenerator(seed);

    // 添加沙丘
    for (let i = 0; i < dunes; i++) {
      // 随机选择沙丘中心
      const centerX = Math.floor(random() * resolution);
      const centerZ = Math.floor(random() * resolution);

      // 沙丘大小
      const width = duneWidth * (0.5 + random() * 0.5);
      const height = duneHeight * (0.5 + random() * 0.5);

      // 沙丘方向
      const angle = random() * Math.PI * 2;
      const dirX = Math.cos(angle);
      const dirZ = Math.sin(angle);

      // 应用沙丘
      for (let z = 0; z < resolution; z++) {
        for (let x = 0; x < resolution; x++) {
          // 计算到沙丘中心的距离
          const dx = x - centerX;
          const dz = z - centerZ;

          // 计算沿沙丘方向和垂直于沙丘方向的距离
          const alongDist = dx * dirX + dz * dirZ;
          const perpDist = Math.abs(dx * dirZ - dz * dirX);

          // 如果在沙丘范围内
          if (Math.abs(alongDist) < width && perpDist < width * 0.5) {
            const index = z * resolution + x;

            // 计算沙丘高度（沿方向呈正弦波形）
            const duneHeightFactor = Math.cos(alongDist / width * Math.PI) * (1 - perpDist / (width * 0.5));

            // 应用沙丘高度
            heightData[index] += height * duneHeightFactor;

            // 确保高度不会超过1
            heightData[index] = Math.min(1, heightData[index]);
          }
        }
      }
    }

    // 标记地形需要更新
    terrain.needsUpdate = true;
  }

  /**
   * 生成地下河
   * @param terrain 地形组件
   * @param params 地下河生成参数
   */
  public static generateUndergroundRivers(terrain: TerrainComponent, params: UndergroundRiverParams): void {
    // 调用地下河生成器
    UndergroundRiverGenerator.generateUndergroundRivers(terrain, params);
  }

  /**
   * 生成地下湖泊
   * @param terrain 地形组件
   * @param params 地下湖泊生成参数
   */
  public static generateUndergroundLakes(terrain: TerrainComponent, params: UndergroundLakeParams): void {
    // 调用地下湖泊生成器
    UndergroundLakeGenerator.generateUndergroundLakes(terrain, params);
  }

  /**
   * 生成岛屿地形
   * @param terrain 地形组件
   * @param params 参数
   */
  private static generateIslandTerrain(terrain: TerrainComponent, params: any): void {
    const { radius, height, falloff } = params;
    const resolution = terrain.resolution;
    const heightData = terrain.heightData;
    const seed = params.seed || Math.floor(Math.random() * 1000);

    // 岛屿中心
    const centerX = resolution / 2;
    const centerZ = resolution / 2;

    // 创建随机数生成器
    const random = this.createRandomGenerator(seed);

    // 对每个点应用岛屿高度
    for (let z = 0; z < resolution; z++) {
      for (let x = 0; x < resolution; x++) {
        const index = z * resolution + x;

        // 计算到岛屿中心的距离
        const dx = x - centerX;
        const dz = z - centerZ;
        const distance = Math.sqrt(dx * dx + dz * dz);

        // 如果在岛屿半径内
        if (distance <= radius) {
          // 计算高度因子（基于距离）
          const distanceFactor = distance / radius;
          const heightFactor = Math.pow(1 - distanceFactor, falloff);

          // 添加一些噪声
          const noiseValue = this.simplexNoise(x * 0.01, z * 0.01, seed) * 0.2;

          // 应用岛屿高度
          heightData[index] = height * heightFactor + noiseValue;

          // 确保高度在有效范围内
          heightData[index] = Math.min(1, Math.max(0, heightData[index]));
        } else {
          // 岛屿外是水
          heightData[index] = 0;
        }
      }
    }

    // 标记地形需要更新
    terrain.needsUpdate = true;
  }

  /**
   * 简单的Simplex噪声实现
   * @param x X坐标
   * @param y Y坐标
   * @param seed 种子
   * @returns 噪声值
   */
  private static simplexNoise(x: number, y: number, seed: number): number {
    // 简化的Simplex噪声实现
    const random = this.createRandomGenerator(seed);
    const dot = (g: number[], x: number, y: number) => g[0] * x + g[1] * y;

    // 创建梯度
    const grad3 = [
      [1, 1], [-1, 1], [1, -1], [-1, -1],
      [1, 0], [-1, 0], [0, 1], [0, -1]
    ];

    // 获取梯度
    const getGrad = (i: number, j: number) => {
      const idx = Math.floor(random() * 8);
      return grad3[idx];
    };

    // 计算噪声
    const F2 = 0.5 * (Math.sqrt(3) - 1);
    const G2 = (3 - Math.sqrt(3)) / 6;

    const s = (x + y) * F2;
    const i = Math.floor(x + s);
    const j = Math.floor(y + s);

    const t = (i + j) * G2;
    const X0 = i - t;
    const Y0 = j - t;
    const x0 = x - X0;
    const y0 = y - Y0;

    let i1, j1;
    if (x0 > y0) {
      i1 = 1;
      j1 = 0;
    } else {
      i1 = 0;
      j1 = 1;
    }

    const x1 = x0 - i1 + G2;
    const y1 = y0 - j1 + G2;
    const x2 = x0 - 1 + 2 * G2;
    const y2 = y0 - 1 + 2 * G2;

    const g0 = getGrad(i, j);
    const g1 = getGrad(i + i1, j + j1);
    const g2 = getGrad(i + 1, j + 1);

    const n0 = Math.max(0, 0.5 - x0 * x0 - y0 * y0);
    const n1 = Math.max(0, 0.5 - x1 * x1 - y1 * y1);
    const n2 = Math.max(0, 0.5 - x2 * x2 - y2 * y2);

    const noise = 70 * (n0 * n0 * n0 * n0 * dot(g0, x0, y0) +
                        n1 * n1 * n1 * n1 * dot(g1, x1, y1) +
                        n2 * n2 * n2 * n2 * dot(g2, x2, y2));

    // 归一化到[-1, 1]范围
    return (noise + 1) / 2;
  }
}