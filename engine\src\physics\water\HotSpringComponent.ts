/**
 * 温泉组件
 * 用于表示温泉及其特殊物理属性和效果
 */
import * as THREE from 'three';
import { WaterBodyComponent, WaterBodyType } from './WaterBodyComponent';
import type { Entity } from '../../core/Entity';
import { Debug } from '../../utils/Debug';
import { AudioSource, AudioSourceOptions } from '../../audio/AudioSource';
import { AudioType } from '../../audio/AudioSystem';

/**
 * 温泉配置
 */
export interface HotSpringConfig {
  /** 温泉类型 */
  hotSpringType?: HotSpringType;
  /** 温泉宽度 */
  width?: number;
  /** 温泉高度 */
  height?: number;
  /** 温泉深度 */
  depth?: number;
  /** 温泉位置 */
  position?: THREE.Vector3;
  /** 温泉旋转 */
  rotation?: THREE.Euler;
  /** 温泉颜色 */
  color?: THREE.Color;
  /** 温泉不透明度 */
  opacity?: number;
  /** 温泉温度 */
  temperature?: number;
  /** 温泉波动强度 */
  waveAmplitude?: number;
  /** 温泉波动频率 */
  waveFrequency?: number;
  /** 温泉波动速度 */
  waveSpeed?: number;
  /** 是否启用气泡效果 */
  enableBubbleEffect?: boolean;
  /** 气泡效果强度 */
  bubbleEffectStrength?: number;
  /** 气泡大小范围 */
  bubbleSizeRange?: [number, number];
  /** 气泡速度范围 */
  bubbleSpeedRange?: [number, number];
  /** 气泡密度 */
  bubbleDensity?: number;
  /** 气泡分布范围 */
  bubbleDistributionRadius?: number;
  /** 是否启用气泡爆裂效果 */
  enableBubbleBurstEffect?: boolean;
  /** 气泡爆裂效果强度 */
  bubbleBurstEffectStrength?: number;
  /** 是否启用水蒸气效果 */
  enableSteamEffect?: boolean;
  /** 水蒸气效果强度 */
  steamEffectStrength?: number;
  /** 水蒸气颜色 */
  steamColor?: THREE.Color;
  /** 水蒸气密度 */
  steamDensity?: number;
  /** 水蒸气大小范围 */
  steamSizeRange?: [number, number];
  /** 水蒸气速度范围 */
  steamSpeedRange?: [number, number];
  /** 水蒸气上升高度 */
  steamRiseHeight?: number;
  /** 是否启用声音效果 */
  enableSoundEffect?: boolean;
  /** 声音效果音量 */
  soundEffectVolume?: number;
  /** 是否启用热扩散效果 */
  enableHeatDiffusion?: boolean;
  /** 热扩散范围 */
  heatDiffusionRange?: number;
  /** 是否启用矿物质效果 */
  enableMineralEffect?: boolean;
  /** 矿物质颜色 */
  mineralColor?: THREE.Color;
}

/**
 * 温泉类型
 */
export enum HotSpringType {
  /** 标准温泉 */
  STANDARD = 'standard',
  /** 高温温泉 */
  HIGH_TEMPERATURE = 'high_temperature',
  /** 低温温泉 */
  LOW_TEMPERATURE = 'low_temperature',
  /** 硫磺温泉 */
  SULFUR = 'sulfur',
  /** 矿物质温泉 */
  MINERAL = 'mineral',
  /** 地下温泉 */
  UNDERGROUND = 'underground'
}

/**
 * 温泉组件
 */
export class HotSpringComponent extends WaterBodyComponent {
  /** 温泉类型 */
  private hotSpringType: HotSpringType = HotSpringType.STANDARD;
  /** 温泉温度 */
  private temperature: number = 60.0; // 摄氏度
  /** 温泉波动强度 */
  private waveAmplitude: number = 0.1;
  /** 温泉波动频率 */
  private waveFrequency: number = 2.0;
  /** 温泉波动速度 */
  private waveSpeed: number = 0.5;
  /** 是否启用气泡效果 */
  private enableBubbleEffect: boolean = true;
  /** 气泡效果强度 */
  private bubbleEffectStrength: number = 1.0;
  /** 气泡大小范围 */
  private bubbleSizeRange: [number, number] = [0.05, 0.2];
  /** 气泡速度范围 */
  private bubbleSpeedRange: [number, number] = [0.1, 0.3];
  /** 气泡密度 */
  private bubbleDensity: number = 1.0;
  /** 气泡分布范围 */
  private bubbleDistributionRadius: number = 0.8;
  /** 是否启用气泡爆裂效果 */
  private enableBubbleBurstEffect: boolean = true;
  /** 气泡爆裂效果强度 */
  private bubbleBurstEffectStrength: number = 1.0;
  /** 是否启用水蒸气效果 */
  private enableSteamEffect: boolean = true;
  /** 水蒸气效果强度 */
  private steamEffectStrength: number = 1.0;
  /** 水蒸气颜色 */
  private steamColor: THREE.Color = new THREE.Color(0xffffff);
  /** 水蒸气密度 */
  private steamDensity: number = 1.0;
  /** 水蒸气大小范围 */
  private steamSizeRange: [number, number] = [0.5, 1.5];
  /** 水蒸气速度范围 */
  private steamSpeedRange: [number, number] = [0.05, 0.1];
  /** 水蒸气上升高度 */
  private steamRiseHeight: number = 2.0;
  /** 是否启用声音效果 */
  private enableSoundEffect: boolean = true;
  /** 声音效果音量 */
  private soundEffectVolume: number = 1.0;
  /** 是否启用热扩散效果 */
  private enableHeatDiffusion: boolean = true;
  /** 热扩散范围 */
  private heatDiffusionRange: number = 5.0;
  /** 是否启用矿物质效果 */
  private enableMineralEffect: boolean = true;
  /** 矿物质颜色 */
  private mineralColor: THREE.Color = new THREE.Color(0xc0a080);
  /** 音频源 */
  private audioSource: AudioSource | null = null;
  /** 气泡粒子系统 */
  private bubbleParticleSystem: any = null;
  /** 水蒸气粒子系统 */
  private steamParticleSystem: any = null;
  /** 矿物质边缘 */
  private mineralEdge: THREE.Mesh | null = null;
  /** 热扩散区域 */
  private heatDiffusionArea: THREE.Mesh | null = null;

  /**
   * 创建温泉组件
   * @param entity 实体
   * @param config 温泉配置
   */
  constructor(entity: Entity, config: HotSpringConfig = {}) {
    super(entity);

    // 设置水体类型为温泉
    this.type = WaterBodyType.HOT_SPRING;

    // 应用配置
    this.applyConfig(config);

    // 初始化温泉特有属性
    this.initialize();
  }

  /**
   * 应用配置
   * @param config 温泉配置
   */
  private applyConfig(config: HotSpringConfig): void {
    // 应用基本配置
    if (config.width !== undefined) this.size.width = config.width;
    if (config.height !== undefined) this.size.height = config.height;
    if (config.depth !== undefined) this.size.depth = config.depth;
    if (config.position) this.position.copy(config.position);
    if (config.rotation) this.rotation.copy(config.rotation);
    if (config.color) this.setColor(config.color);
    if (config.opacity !== undefined) this.setOpacity(config.opacity);

    // 应用温泉特有配置
    if (config.hotSpringType !== undefined) this.hotSpringType = config.hotSpringType;
    if (config.temperature !== undefined) this.temperature = config.temperature;
    if (config.waveAmplitude !== undefined) this.waveAmplitude = config.waveAmplitude;
    if (config.waveFrequency !== undefined) this.waveFrequency = config.waveFrequency;
    if (config.waveSpeed !== undefined) this.waveSpeed = config.waveSpeed;

    // 应用气泡效果配置
    if (config.enableBubbleEffect !== undefined) this.enableBubbleEffect = config.enableBubbleEffect;
    if (config.bubbleEffectStrength !== undefined) this.bubbleEffectStrength = config.bubbleEffectStrength;
    if (config.bubbleSizeRange !== undefined) this.bubbleSizeRange = config.bubbleSizeRange;
    if (config.bubbleSpeedRange !== undefined) this.bubbleSpeedRange = config.bubbleSpeedRange;
    if (config.bubbleDensity !== undefined) this.bubbleDensity = config.bubbleDensity;
    if (config.bubbleDistributionRadius !== undefined) this.bubbleDistributionRadius = config.bubbleDistributionRadius;

    // 应用气泡爆裂效果配置
    if (config.enableBubbleBurstEffect !== undefined) this.enableBubbleBurstEffect = config.enableBubbleBurstEffect;
    if (config.bubbleBurstEffectStrength !== undefined) this.bubbleBurstEffectStrength = config.bubbleBurstEffectStrength;

    // 应用水蒸气效果配置
    if (config.enableSteamEffect !== undefined) this.enableSteamEffect = config.enableSteamEffect;
    if (config.steamEffectStrength !== undefined) this.steamEffectStrength = config.steamEffectStrength;
    if (config.steamColor) this.steamColor = config.steamColor;
    if (config.steamDensity !== undefined) this.steamDensity = config.steamDensity;
    if (config.steamSizeRange !== undefined) this.steamSizeRange = config.steamSizeRange;
    if (config.steamSpeedRange !== undefined) this.steamSpeedRange = config.steamSpeedRange;
    if (config.steamRiseHeight !== undefined) this.steamRiseHeight = config.steamRiseHeight;

    // 应用其他效果配置
    if (config.enableSoundEffect !== undefined) this.enableSoundEffect = config.enableSoundEffect;
    if (config.soundEffectVolume !== undefined) this.soundEffectVolume = config.soundEffectVolume;
    if (config.enableHeatDiffusion !== undefined) this.enableHeatDiffusion = config.enableHeatDiffusion;
    if (config.heatDiffusionRange !== undefined) this.heatDiffusionRange = config.heatDiffusionRange;
    if (config.enableMineralEffect !== undefined) this.enableMineralEffect = config.enableMineralEffect;
    if (config.mineralColor) this.mineralColor = config.mineralColor;
  }

  /**
   * 初始化温泉组件
   */
  public initialize(): void {
    // 调用父类初始化
    super.initialize();

    // 设置波动参数
    this.setWaveParams({
      amplitude: this.waveAmplitude,
      frequency: this.waveFrequency,
      speed: this.waveSpeed,
      direction: new THREE.Vector2(1, 1)
    });

    // 初始化音频
    this.initializeAudio();

    // 初始化粒子系统
    this.initializeParticleSystems();

    // 创建矿物质边缘
    this.createMineralEdge();

    // 创建热扩散区域
    this.createHeatDiffusionArea();

    Debug.log('HotSpringComponent', '温泉组件初始化完成');
  }

  /**
   * 初始化音频
   */
  private initializeAudio(): void {
    // 如果未启用声音效果，则不初始化音频
    if (!this.enableSoundEffect) return;

    // 获取音频系统
    const audioSystem = this.entity.getWorld().getSystem('AudioSystem');
    if (!audioSystem) return;

    // 创建音频源
    const audioSourceId = `hotspring_${this.entity.id}`;
    this.audioSource = audioSystem.createSource(audioSourceId, AudioType.AMBIENT);
    if (!this.audioSource) return;

    // 设置音频源属性
    this.audioSource.setVolume(this.soundEffectVolume);
    this.audioSource.setLoop(true);
    this.audioSource.setSpatial(true);
    this.audioSource.setPosition(this.getPosition());
    this.audioSource.setRefDistance(10);
    this.audioSource.setMaxDistance(50);
    this.audioSource.setRolloffFactor(1);

    // 播放温泉声音
    audioSystem.play(audioSourceId, 'sounds/hotspring.mp3', {
      loop: true,
      volume: this.soundEffectVolume
    });

    Debug.log('HotSpringComponent', '初始化音频完成');
  }

  /**
   * 初始化粒子系统
   */
  private initializeParticleSystems(): void {
    // 获取水下粒子系统
    const underwaterParticleSystem = this.entity.getWorld().getSystem('UnderwaterParticleSystem');
    if (!underwaterParticleSystem) return;

    // 获取温泉位置
    const position = this.getPosition();

    // 根据温泉类型调整粒子效果
    this.initializeParticlesByType(underwaterParticleSystem, position);

    Debug.log('HotSpringComponent', '初始化粒子系统完成');
  }

  /**
   * 根据温泉类型初始化粒子效果
   * @param underwaterParticleSystem 水下粒子系统
   * @param position 温泉位置
   */
  private initializeParticlesByType(underwaterParticleSystem: any, position: THREE.Vector3): void {
    // 创建气泡效果
    if (this.enableBubbleEffect) {
      // 根据温泉类型调整气泡颜色和效果
      let bubbleColor = 0xffffff;
      let bubbleOpacity = 0.7 * this.bubbleEffectStrength;
      let bubbleCount = Math.floor(200 * this.bubbleEffectStrength * this.bubbleDensity);
      let bubbleSize = [...this.bubbleSizeRange];
      let bubbleSpeed = [...this.bubbleSpeedRange];
      let bubbleAcceleration = new THREE.Vector3(0, 0.1, 0);
      let bubbleDistributionRadius = this.size.width / 2 * this.bubbleDistributionRadius;

      switch (this.hotSpringType) {
        case HotSpringType.HIGH_TEMPERATURE:
          bubbleCount = Math.floor(300 * this.bubbleEffectStrength * this.bubbleDensity);
          bubbleSpeed = [this.bubbleSpeedRange[0] * 1.5, this.bubbleSpeedRange[1] * 1.5];
          bubbleAcceleration = new THREE.Vector3(0, 0.15, 0);
          break;
        case HotSpringType.LOW_TEMPERATURE:
          bubbleCount = Math.floor(100 * this.bubbleEffectStrength * this.bubbleDensity);
          bubbleSpeed = [this.bubbleSpeedRange[0] * 0.7, this.bubbleSpeedRange[1] * 0.7];
          bubbleAcceleration = new THREE.Vector3(0, 0.08, 0);
          break;
        case HotSpringType.SULFUR:
          bubbleColor = 0xffffaa;
          bubbleOpacity = 0.6 * this.bubbleEffectStrength;
          bubbleSize = [this.bubbleSizeRange[0] * 1.2, this.bubbleSizeRange[1] * 1.2];
          break;
        case HotSpringType.MINERAL:
          bubbleColor = 0xaaddff;
          bubbleSize = [this.bubbleSizeRange[0] * 0.8, this.bubbleSizeRange[1] * 1.1];
          break;
        case HotSpringType.UNDERGROUND:
          bubbleColor = 0xaaaaff;
          bubbleOpacity = 0.5 * this.bubbleEffectStrength;
          bubbleDistributionRadius = this.size.width / 2 * this.bubbleDistributionRadius * 0.9;
          break;
      }

      underwaterParticleSystem.addParticleGroup(
        this.entity.id,
        'hotspringBubble',
        {
          type: 'BUBBLE',
          count: bubbleCount,
          size: bubbleSize,
          color: bubbleColor,
          opacity: bubbleOpacity,
          lifetime: [1, 3],
          speed: bubbleSpeed,
          acceleration: bubbleAcceleration,
          blending: THREE.AdditiveBlending,
          emissionArea: {
            shape: 'circle',
            radius: bubbleDistributionRadius,
            position: new THREE.Vector3(position.x, position.y - this.size.height / 2 + 0.1, position.z)
          }
        }
      );

      // 创建气泡爆裂效果
      if (this.enableBubbleBurstEffect) {
        // 根据温泉类型调整气泡爆裂效果
        let burstCount = Math.floor(50 * this.bubbleBurstEffectStrength);
        let burstSize = [this.bubbleSizeRange[0] * 0.4, this.bubbleSizeRange[1] * 0.5];
        let burstSpeed = [this.bubbleSpeedRange[0] * 0.5, this.bubbleSpeedRange[1] * 0.7];
        let burstAcceleration = new THREE.Vector3(0, 0.01, 0);
        let burstRadius = bubbleDistributionRadius * 1.2;

        // 根据温泉类型调整爆裂效果
        switch (this.hotSpringType) {
          case HotSpringType.HIGH_TEMPERATURE:
            burstCount = Math.floor(70 * this.bubbleBurstEffectStrength);
            burstSize = [this.bubbleSizeRange[0] * 0.5, this.bubbleSizeRange[1] * 0.6];
            burstSpeed = [this.bubbleSpeedRange[0] * 0.7, this.bubbleSpeedRange[1] * 0.9];
            burstAcceleration = new THREE.Vector3(0, 0.015, 0);
            break;
          case HotSpringType.SULFUR:
            burstSize = [this.bubbleSizeRange[0] * 0.6, this.bubbleSizeRange[1] * 0.7];
            break;
        }

        underwaterParticleSystem.addParticleGroup(
          this.entity.id,
          'hotspringBubbleBurst',
          {
            type: 'SPLASH',
            count: burstCount,
            size: burstSize,
            color: bubbleColor,
            opacity: bubbleOpacity * 0.8,
            lifetime: [0.5, 1.5],
            speed: burstSpeed,
            acceleration: burstAcceleration,
            blending: THREE.AdditiveBlending,
            emissionArea: {
              shape: 'circle',
              radius: burstRadius,
              position: new THREE.Vector3(position.x, position.y + 0.05, position.z)
            }
          }
        );
      }
    }

    // 创建水蒸气效果
    if (this.enableSteamEffect) {
      // 根据温泉类型调整水蒸气颜色和效果
      let steamColor = this.steamColor.getHex();
      let steamOpacity = 0.3 * this.steamEffectStrength;
      let steamCount = Math.floor(100 * this.steamEffectStrength * this.steamDensity);
      let steamSize = [...this.steamSizeRange];
      let steamSpeed = [...this.steamSpeedRange];
      let steamAcceleration = new THREE.Vector3(0, 0.02, 0);
      let steamRiseHeight = this.steamRiseHeight;
      let steamRadius = this.size.width / 2 * 0.9;

      switch (this.hotSpringType) {
        case HotSpringType.HIGH_TEMPERATURE:
          steamCount = Math.floor(150 * this.steamEffectStrength * this.steamDensity);
          steamSize = [this.steamSizeRange[0] * 1.2, this.steamSizeRange[1] * 1.3];
          steamSpeed = [this.steamSpeedRange[0] * 1.5, this.steamSpeedRange[1] * 1.5];
          steamAcceleration = new THREE.Vector3(0, 0.03, 0);
          steamRiseHeight = this.steamRiseHeight * 1.3;
          break;
        case HotSpringType.LOW_TEMPERATURE:
          steamCount = Math.floor(50 * this.steamEffectStrength * this.steamDensity);
          steamSize = [this.steamSizeRange[0] * 0.7, this.steamSizeRange[1] * 0.8];
          steamSpeed = [this.steamSpeedRange[0] * 0.7, this.steamSpeedRange[1] * 0.7];
          steamOpacity = 0.2 * this.steamEffectStrength;
          steamRiseHeight = this.steamRiseHeight * 0.7;
          break;
        case HotSpringType.SULFUR:
          steamColor = 0xffffdd;
          steamOpacity = 0.4 * this.steamEffectStrength;
          steamSize = [this.steamSizeRange[0] * 1.1, this.steamSizeRange[1] * 1.2];
          break;
        case HotSpringType.MINERAL:
          steamColor = 0xccddff;
          steamSize = [this.steamSizeRange[0] * 0.9, this.steamSizeRange[1] * 1.1];
          break;
        case HotSpringType.UNDERGROUND:
          steamColor = 0xaabbcc;
          steamOpacity = 0.25 * this.steamEffectStrength;
          steamRadius = this.size.width / 2 * 0.8;
          break;
      }

      // 调整水蒸气上升高度
      const steamYPosition = position.y + 0.1;
      const steamLifetime = [3, 8]; // 基础生命周期

      // 根据温度调整水蒸气生命周期和上升高度
      const temperatureFactor = this.temperature / 60;
      const adjustedLifetime = [
        steamLifetime[0] * temperatureFactor,
        steamLifetime[1] * temperatureFactor
      ];

      // 创建水蒸气粒子组
      underwaterParticleSystem.addParticleGroup(
        this.entity.id,
        'hotspringSteam',
        {
          type: 'STEAM',
          count: steamCount,
          size: steamSize,
          color: steamColor,
          opacity: steamOpacity,
          lifetime: adjustedLifetime,
          speed: steamSpeed,
          acceleration: steamAcceleration,
          blending: THREE.AdditiveBlending,
          emissionArea: {
            shape: 'circle',
            radius: steamRadius,
            position: new THREE.Vector3(position.x, steamYPosition, position.z)
          },
          // 添加自定义属性
          customData: {
            riseHeight: steamRiseHeight,
            temperature: this.temperature
          }
        }
      );
    }
  }

  /**
   * 创建矿物质边缘
   */
  private createMineralEdge(): void {
    // 如果未启用矿物质效果，则不创建矿物质边缘
    if (!this.enableMineralEffect) return;

    // 获取温泉位置
    const position = this.getPosition();

    // 创建矿物质边缘几何体
    const geometry = new THREE.RingGeometry(
      this.size.width / 2,
      this.size.width / 2 + 1.5,
      32,
      1
    );

    // 创建矿物质边缘材质
    const material = new THREE.MeshStandardMaterial({
      color: this.mineralColor,
      roughness: 0.8,
      metalness: 0.2,
      side: THREE.DoubleSide
    });

    // 创建矿物质边缘网格
    this.mineralEdge = new THREE.Mesh(geometry, material);
    this.mineralEdge.position.copy(position);
    (this.mineralEdge as any).getPosition().y = position.y - this.size.height / 2 + 0.05;
    this.mineralEdge.rotation.x = -Math.PI / 2;

    // 添加到实体
    this.entity.addObject(this.mineralEdge);

    Debug.log('HotSpringComponent', '创建矿物质边缘完成');
  }

  /**
   * 创建热扩散区域
   */
  private createHeatDiffusionArea(): void {
    // 如果未启用热扩散效果，则不创建热扩散区域
    if (!this.enableHeatDiffusion) return;

    // 获取温泉位置
    const position = this.getPosition();

    // 创建热扩散区域几何体
    const geometry = new THREE.CircleGeometry(this.heatDiffusionRange, 32);

    // 创建热扩散区域材质
    const material = new THREE.MeshBasicMaterial({
      color: 0xffffff,
      transparent: true,
      opacity: 0.1,
      side: THREE.DoubleSide,
      depthWrite: false
    });

    // 创建热扩散区域网格
    this.heatDiffusionArea = new THREE.Mesh(geometry, material);
    this.heatDiffusionArea.position.copy(position);
    (this.heatDiffusionArea as any).getPosition().y = position.y - this.size.height / 2 + 0.1;
    this.heatDiffusionArea.rotation.x = -Math.PI / 2;

    // 添加到实体
    this.entity.addObject(this.heatDiffusionArea);

    Debug.log('HotSpringComponent', '创建热扩散区域完成');
  }

  /**
   * 更新温泉组件
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 调用父类更新
    super.update(deltaTime);

    // 更新粒子效果
    this.updateParticleEffects(deltaTime);

    // 更新热扩散效果
    this.updateHeatDiffusion(deltaTime);

    // 更新音频
    this.updateAudio(deltaTime);
  }

  /**
   * 更新粒子效果
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateParticleEffects(deltaTime: number): void {
    // 获取水下粒子系统
    const underwaterParticleSystem = this.entity.getWorld().getSystem('UnderwaterParticleSystem');
    if (!underwaterParticleSystem) return;

    // 获取温泉位置
    const position = this.getPosition();

    // 更新气泡粒子位置
    if (this.enableBubbleEffect) {
      // 更新气泡位置
      underwaterParticleSystem.updateParticleGroupPosition(
        this.entity.id,
        'hotspringBubble',
        new THREE.Vector3(position.x, position.y - this.size.height / 2 + 0.1, position.z)
      );

      // 根据温度和密度调整气泡发射率
      const bubbleEmissionRate = Math.floor(50 * this.bubbleEffectStrength * this.bubbleDensity * (this.temperature / 60));
      underwaterParticleSystem.updateParticleGroupProperty(
        this.entity.id,
        'hotspringBubble',
        'emissionRate',
        bubbleEmissionRate
      );

      // 随机调整气泡大小和速度，使效果更自然
      const timeFactor = Math.sin(Date.now() * 0.001) * 0.1 + 1.0; // 0.9 到 1.1 之间的波动
      const sizeVariation = [
        this.bubbleSizeRange[0] * (0.9 + Math.random() * 0.2),
        this.bubbleSizeRange[1] * (0.9 + Math.random() * 0.2)
      ];

      underwaterParticleSystem.updateParticleGroupProperty(
        this.entity.id,
        'hotspringBubble',
        'size',
        sizeVariation
      );

      // 更新气泡爆裂效果
      if (this.enableBubbleBurstEffect) {
        // 更新气泡爆裂位置
        underwaterParticleSystem.updateParticleGroupPosition(
          this.entity.id,
          'hotspringBubbleBurst',
          new THREE.Vector3(position.x, position.y + 0.05, position.z)
        );

        // 根据温度调整气泡爆裂效果发射率
        const burstEmissionRate = Math.floor(20 * this.bubbleBurstEffectStrength * (this.temperature / 60));
        underwaterParticleSystem.updateParticleGroupProperty(
          this.entity.id,
          'hotspringBubbleBurst',
          'emissionRate',
          burstEmissionRate
        );

        // 随机调整爆裂效果大小和速度
        const burstSizeVariation = [
          this.bubbleSizeRange[0] * 0.4 * (0.9 + Math.random() * 0.2),
          this.bubbleSizeRange[1] * 0.5 * (0.9 + Math.random() * 0.2)
        ];

        underwaterParticleSystem.updateParticleGroupProperty(
          this.entity.id,
          'hotspringBubbleBurst',
          'size',
          burstSizeVariation
        );
      }
    }

    // 更新水蒸气粒子位置
    if (this.enableSteamEffect) {
      // 更新水蒸气位置
      underwaterParticleSystem.updateParticleGroupPosition(
        this.entity.id,
        'hotspringSteam',
        new THREE.Vector3(position.x, position.y + 0.1, position.z)
      );

      // 根据温度和密度调整水蒸气强度
      const steamStrength = this.steamEffectStrength * this.steamDensity * (this.temperature / 100);
      underwaterParticleSystem.updateParticleGroupProperty(
        this.entity.id,
        'hotspringSteam',
        'emissionRate',
        Math.floor(50 * steamStrength)
      );

      // 根据温泉类型和温度调整水蒸气颜色
      let steamColor = this.steamColor.clone();

      // 高温时颜色偏白
      if (this.temperature > 80) {
        steamColor.lerp(new THREE.Color(0xffffff), 0.3);
      }

      // 硫磺温泉颜色偏黄
      if (this.hotSpringType === HotSpringType.SULFUR) {
        steamColor.lerp(new THREE.Color(0xffffaa), 0.5);
      }

      // 随时间轻微变化水蒸气颜色，使效果更自然
      const timeFactor = Math.sin(Date.now() * 0.0005) * 0.05;
      steamColor.offsetHSL(0, 0, timeFactor);

      underwaterParticleSystem.updateParticleGroupProperty(
        this.entity.id,
        'hotspringSteam',
        'color',
        steamColor.getHex()
      );

      // 随机调整水蒸气大小和速度
      const steamSizeVariation = [
        this.steamSizeRange[0] * (0.9 + Math.random() * 0.2),
        this.steamSizeRange[1] * (0.9 + Math.random() * 0.2)
      ];

      underwaterParticleSystem.updateParticleGroupProperty(
        this.entity.id,
        'hotspringSteam',
        'size',
        steamSizeVariation
      );

      // 根据环境温度调整水蒸气上升高度
      // 这里假设有一个环境温度系统，如果没有则使用默认值
      const environmentSystem = this.entity.getWorld().getSystem('EnvironmentSystem');
      let environmentTemperature = 20; // 默认环境温度

      if (environmentSystem && environmentSystem.getTemperature) {
        environmentTemperature = environmentSystem.getTemperature();
      }

      // 温差越大，水蒸气上升高度越高
      const temperatureDifference = Math.max(0, this.temperature - environmentTemperature);
      const riseHeightFactor = 1.0 + (temperatureDifference / 100) * 0.5; // 最多增加50%

      underwaterParticleSystem.updateParticleGroupProperty(
        this.entity.id,
        'hotspringSteam',
        'customData',
        {
          riseHeight: this.steamRiseHeight * riseHeightFactor,
          temperature: this.temperature
        }
      );
    }
  }

  /**
   * 更新热扩散效果
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateHeatDiffusion(deltaTime: number): void {
    // 如果未启用热扩散效果或没有热扩散区域，则不更新
    if (!this.enableHeatDiffusion || !this.heatDiffusionArea) return;

    // 获取物理系统
    const physicsSystem = this.entity.getWorld().getSystem('PhysicsSystem');
    if (!physicsSystem) return;

    // 获取温泉位置
    const position = this.getPosition();

    // 获取热扩散区域内的实体
    const entities = physicsSystem.getEntitiesInRadius(position, this.heatDiffusionRange);

    // 对每个实体应用热效果
    for (const entity of entities) {
      // 跳过温泉自身
      if (entity === this.entity) continue;

      // 获取实体到温泉的距离
      const distance = entity.getPosition().distanceTo(position);

      // 计算热效果强度（距离越近，效果越强）
      const heatStrength = 1 - (distance / this.heatDiffusionRange);

      // 应用热效果
      this.applyHeatEffect(entity, heatStrength, deltaTime);
    }

    // 更新热扩散区域的可视效果
    const material = this.heatDiffusionArea.material as THREE.MeshBasicMaterial;
    if (material) {
      // 根据温度调整热扩散区域的颜色
      const hue = 0.05; // 红色偏黄
      const saturation = 0.6;
      const lightness = 0.5 + 0.2 * Math.sin(Date.now() * 0.001);
      material.color.setHSL(hue, saturation, lightness);
    }
  }

  /**
   * 应用热效果
   * @param entity 实体
   * @param strength 强度
   * @param deltaTime 帧间隔时间（秒）
   */
  private applyHeatEffect(entity: Entity, strength: number, deltaTime: number): void {
    // 获取实体的健康组件（如果有）
    const healthComponent = entity.getComponent('HealthComponent') as any;
    if (healthComponent) {
      // 根据温泉温度和强度计算恢复量
      const recoveryAmount = strength * (this.temperature / 100) * deltaTime * 5;

      // 恢复生命值
      healthComponent.heal(recoveryAmount);
    }

    // 获取实体的状态组件（如果有）
    const statusComponent = entity.getComponent('StatusComponent') as any;
    if (statusComponent) {
      // 添加"温暖"状态
      statusComponent.addStatus('warm', strength * 10, this.temperature / 10);
    }
  }

  /**
   * 更新音频
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateAudio(deltaTime: number): void {
    // 如果未启用声音效果或没有音频源，则不更新
    if (!this.enableSoundEffect || !this.audioSource) return;

    // 更新音频源位置
    this.audioSource.setPosition(this.getPosition());

    // 根据温泉温度调整音量
    const volumeFactor = Math.min(1.0, this.temperature / 80);
    this.audioSource.setVolume(this.soundEffectVolume * volumeFactor);
  }

  /**
   * 销毁温泉组件
   */
  public destroy(): void {
    // 停止音频
    if (this.audioSource) {
      this.audioSource.stop();
      this.audioSource = null;
    }

    // 移除矿物质边缘
    if (this.mineralEdge) {
      this.entity.removeObject(this.mineralEdge);
      (this.mineralEdge.geometry as any).dispose();
      (this.mineralEdge.material as THREE.Material).dispose();
      this.mineralEdge = null;
    }

    // 移除热扩散区域
    if (this.heatDiffusionArea) {
      this.entity.removeObject(this.heatDiffusionArea);
      (this.heatDiffusionArea.geometry as any).dispose();
      (this.heatDiffusionArea.material as THREE.Material).dispose();
      this.heatDiffusionArea = null;
    }

    // 移除粒子系统
    const underwaterParticleSystem = this.entity.getWorld().getSystem('UnderwaterParticleSystem');
    if (underwaterParticleSystem) {
      underwaterParticleSystem.removeParticleGroup(this.entity.id, 'hotspringBubble');
      underwaterParticleSystem.removeParticleGroup(this.entity.id, 'hotspringBubbleBurst');
      underwaterParticleSystem.removeParticleGroup(this.entity.id, 'hotspringSteam');
    }

    // 调用父类销毁
    super.destroy();
  }

  /**
   * 获取温泉温度
   * @returns 温泉温度（摄氏度）
   */
  public getTemperature(): number {
    return this.temperature;
  }

  /**
   * 设置温泉温度
   * @param temperature 温泉温度（摄氏度）
   */
  public setTemperature(temperature: number): void {
    this.temperature = temperature;
  }

  /**
   * 获取波动强度
   * @returns 波动强度
   */
  public getWaveAmplitude(): number {
    return this.waveAmplitude;
  }

  /**
   * 设置波动强度
   * @param amplitude 波动强度
   */
  public setWaveAmplitude(amplitude: number): void {
    this.waveAmplitude = amplitude;
    this.setWaveParams({
      amplitude: this.waveAmplitude,
      frequency: this.waveFrequency,
      speed: this.waveSpeed,
      direction: new THREE.Vector2(1, 1)
    });
  }

  /**
   * 获取波动频率
   * @returns 波动频率
   */
  public getWaveFrequency(): number {
    return this.waveFrequency;
  }

  /**
   * 设置波动频率
   * @param frequency 波动频率
   */
  public setWaveFrequency(frequency: number): void {
    this.waveFrequency = frequency;
    this.setWaveParams({
      amplitude: this.waveAmplitude,
      frequency: this.waveFrequency,
      speed: this.waveSpeed,
      direction: new THREE.Vector2(1, 1)
    });
  }

  /**
   * 获取波动速度
   * @returns 波动速度
   */
  public getWaveSpeed(): number {
    return this.waveSpeed;
  }

  /**
   * 设置波动速度
   * @param speed 波动速度
   */
  public setWaveSpeed(speed: number): void {
    this.waveSpeed = speed;
    this.setWaveParams({
      amplitude: this.waveAmplitude,
      frequency: this.waveFrequency,
      speed: this.waveSpeed,
      direction: new THREE.Vector2(1, 1)
    });
  }

  /**
   * 获取温泉类型
   * @returns 温泉类型
   */
  public getHotSpringType(): HotSpringType {
    return this.hotSpringType;
  }

  /**
   * 设置温泉类型
   * @param type 温泉类型
   */
  public setHotSpringType(type: HotSpringType): void {
    this.hotSpringType = type;

    // 重新初始化粒子系统以应用新类型的效果
    this.destroyParticleSystems();
    this.initializeParticleSystems();
  }

  /**
   * 销毁粒子系统
   */
  private destroyParticleSystems(): void {
    const underwaterParticleSystem = this.entity.getWorld().getSystem('UnderwaterParticleSystem');
    if (underwaterParticleSystem) {
      underwaterParticleSystem.removeParticleGroup(this.entity.id, 'hotspringBubble');
      underwaterParticleSystem.removeParticleGroup(this.entity.id, 'hotspringBubbleBurst');
      underwaterParticleSystem.removeParticleGroup(this.entity.id, 'hotspringSteam');
    }
  }

  /**
   * 获取气泡爆裂效果状态
   * @returns 是否启用气泡爆裂效果
   */
  public isBubbleBurstEffectEnabled(): boolean {
    return this.enableBubbleBurstEffect;
  }

  /**
   * 设置气泡爆裂效果状态
   * @param enabled 是否启用气泡爆裂效果
   */
  public setBubbleBurstEffectEnabled(enabled: boolean): void {
    this.enableBubbleBurstEffect = enabled;

    // 如果禁用，移除气泡爆裂效果
    if (!enabled) {
      const underwaterParticleSystem = this.entity.getWorld().getSystem('UnderwaterParticleSystem');
      if (underwaterParticleSystem) {
        underwaterParticleSystem.removeParticleGroup(this.entity.id, 'hotspringBubbleBurst');
      }
    } else if (this.enableBubbleEffect) {
      // 如果启用且气泡效果也启用，重新创建气泡爆裂效果
      this.initializeParticleSystems();
    }
  }

  /**
   * 获取气泡爆裂效果强度
   * @returns 气泡爆裂效果强度
   */
  public getBubbleBurstEffectStrength(): number {
    return this.bubbleBurstEffectStrength;
  }

  /**
   * 设置气泡爆裂效果强度
   * @param strength 气泡爆裂效果强度
   */
  public setBubbleBurstEffectStrength(strength: number): void {
    this.bubbleBurstEffectStrength = strength;

    // 更新气泡爆裂效果强度
    if (this.enableBubbleBurstEffect) {
      const underwaterParticleSystem = this.entity.getWorld().getSystem('UnderwaterParticleSystem');
      if (underwaterParticleSystem) {
        underwaterParticleSystem.updateParticleGroupProperty(
          this.entity.id,
          'hotspringBubbleBurst',
          'emissionRate',
          Math.floor(20 * strength * (this.temperature / 60))
        );
      }
    }
  }

  /**
   * 获取水蒸气颜色
   * @returns 水蒸气颜色
   */
  public getSteamColor(): THREE.Color {
    return this.steamColor.clone();
  }

  /**
   * 设置水蒸气颜色
   * @param color 水蒸气颜色
   */
  public setSteamColor(color: THREE.Color): void {
    this.steamColor = color.clone();

    // 更新水蒸气颜色
    if (this.enableSteamEffect) {
      const underwaterParticleSystem = this.entity.getWorld().getSystem('UnderwaterParticleSystem');
      if (underwaterParticleSystem) {
        underwaterParticleSystem.updateParticleGroupProperty(
          this.entity.id,
          'hotspringSteam',
          'color',
          color.getHex()
        );
      }
    }
  }

  /**
   * 获取气泡大小范围
   * @returns 气泡大小范围
   */
  public getBubbleSizeRange(): [number, number] {
    return [...this.bubbleSizeRange];
  }

  /**
   * 设置气泡大小范围
   * @param range 气泡大小范围
   */
  public setBubbleSizeRange(range: [number, number]): void {
    this.bubbleSizeRange = [...range];

    // 重新初始化粒子系统以应用新的大小范围
    this.destroyParticleSystems();
    this.initializeParticleSystems();
  }

  /**
   * 获取气泡速度范围
   * @returns 气泡速度范围
   */
  public getBubbleSpeedRange(): [number, number] {
    return [...this.bubbleSpeedRange];
  }

  /**
   * 设置气泡速度范围
   * @param range 气泡速度范围
   */
  public setBubbleSpeedRange(range: [number, number]): void {
    this.bubbleSpeedRange = [...range];

    // 重新初始化粒子系统以应用新的速度范围
    this.destroyParticleSystems();
    this.initializeParticleSystems();
  }

  /**
   * 获取气泡密度
   * @returns 气泡密度
   */
  public getBubbleDensity(): number {
    return this.bubbleDensity;
  }

  /**
   * 设置气泡密度
   * @param density 气泡密度
   */
  public setBubbleDensity(density: number): void {
    this.bubbleDensity = density;

    // 更新气泡发射率
    if (this.enableBubbleEffect) {
      const underwaterParticleSystem = this.entity.getWorld().getSystem('UnderwaterParticleSystem');
      if (underwaterParticleSystem) {
        const bubbleEmissionRate = Math.floor(50 * this.bubbleEffectStrength * this.bubbleDensity * (this.temperature / 60));
        underwaterParticleSystem.updateParticleGroupProperty(
          this.entity.id,
          'hotspringBubble',
          'emissionRate',
          bubbleEmissionRate
        );
      }
    }
  }

  /**
   * 获取气泡分布范围
   * @returns 气泡分布范围
   */
  public getBubbleDistributionRadius(): number {
    return this.bubbleDistributionRadius;
  }

  /**
   * 设置气泡分布范围
   * @param radius 气泡分布范围
   */
  public setBubbleDistributionRadius(radius: number): void {
    this.bubbleDistributionRadius = radius;

    // 重新初始化粒子系统以应用新的分布范围
    this.destroyParticleSystems();
    this.initializeParticleSystems();
  }

  /**
   * 获取水蒸气密度
   * @returns 水蒸气密度
   */
  public getSteamDensity(): number {
    return this.steamDensity;
  }

  /**
   * 设置水蒸气密度
   * @param density 水蒸气密度
   */
  public setSteamDensity(density: number): void {
    this.steamDensity = density;

    // 更新水蒸气发射率
    if (this.enableSteamEffect) {
      const underwaterParticleSystem = this.entity.getWorld().getSystem('UnderwaterParticleSystem');
      if (underwaterParticleSystem) {
        const steamStrength = this.steamEffectStrength * this.steamDensity * (this.temperature / 100);
        underwaterParticleSystem.updateParticleGroupProperty(
          this.entity.id,
          'hotspringSteam',
          'emissionRate',
          Math.floor(50 * steamStrength)
        );
      }
    }
  }

  /**
   * 获取水蒸气大小范围
   * @returns 水蒸气大小范围
   */
  public getSteamSizeRange(): [number, number] {
    return [...this.steamSizeRange];
  }

  /**
   * 设置水蒸气大小范围
   * @param range 水蒸气大小范围
   */
  public setSteamSizeRange(range: [number, number]): void {
    this.steamSizeRange = [...range];

    // 重新初始化粒子系统以应用新的大小范围
    this.destroyParticleSystems();
    this.initializeParticleSystems();
  }

  /**
   * 获取水蒸气速度范围
   * @returns 水蒸气速度范围
   */
  public getSteamSpeedRange(): [number, number] {
    return [...this.steamSpeedRange];
  }

  /**
   * 设置水蒸气速度范围
   * @param range 水蒸气速度范围
   */
  public setSteamSpeedRange(range: [number, number]): void {
    this.steamSpeedRange = [...range];

    // 重新初始化粒子系统以应用新的速度范围
    this.destroyParticleSystems();
    this.initializeParticleSystems();
  }

  /**
   * 获取水蒸气上升高度
   * @returns 水蒸气上升高度
   */
  public getSteamRiseHeight(): number {
    return this.steamRiseHeight;
  }

  /**
   * 设置水蒸气上升高度
   * @param height 水蒸气上升高度
   */
  public setSteamRiseHeight(height: number): void {
    this.steamRiseHeight = height;

    // 更新水蒸气上升高度
    if (this.enableSteamEffect) {
      const underwaterParticleSystem = this.entity.getWorld().getSystem('UnderwaterParticleSystem');
      if (underwaterParticleSystem) {
        underwaterParticleSystem.updateParticleGroupProperty(
          this.entity.id,
          'hotspringSteam',
          'customData',
          {
            riseHeight: this.steamRiseHeight,
            temperature: this.temperature
          }
        );
      }
    }
  }
}
