/**
 * 渲染器类
 * 负责渲染场景
 */
import * as THREE from 'three';
import { Scene } from '../scene/Scene';
import { type Camera  } from './Camera';
import { EventEmitter } from '../utils/EventEmitter';

export interface RendererOptions {
  /** 画布元素或ID */
  canvas?: HTMLCanvasElement | string;
  /** 宽度 */
  width?: number;
  /** 高度 */
  height?: number;
  /** 是否抗锯齿 */
  antialias?: boolean;
  /** 是否使用Alpha通道 */
  alpha?: boolean;
  /** 是否使用深度测试 */
  depth?: boolean;
  /** 是否使用模板测试 */
  stencil?: boolean;
  /** 是否使用对数深度缓冲 */
  logarithmicDepthBuffer?: boolean;
  /** 是否自动清除 */
  autoClear?: boolean;
  /** 是否自动调整大小 */
  autoResize?: boolean;
  /** 像素比例 */
  pixelRatio?: number;
  /** 阴影类型 */
  shadowMapType?: THREE.ShadowMapType;
  /** 是否启用阴影 */
  shadows?: boolean;
  /** 色调映射 */
  toneMapping?: THREE.ToneMapping;
  /** 色调映射曝光 */
  toneMappingExposure?: number;
  /** 输出颜色空间 */
  outputColorSpace?: THREE.ColorSpace;
}

export class Renderer extends EventEmitter {
  /** Three.js渲染器 */
  private renderer: THREE.WebGLRenderer;

  /** 画布元素 */
  private canvas: HTMLCanvasElement;

  /** 是否自动调整大小 */
  private autoResize: boolean;

  /** 调整大小监听器 */
  private resizeListener: (() => void) | null = null;

  /**
   * 创建渲染器实例
   * @param options 渲染器选项
   */
  constructor(options: RendererOptions = {}) {
    super();

    // 获取画布元素
    if (options.canvas) {
      if (typeof options.canvas === 'string') {
        const element = document.getElementById(options.canvas);
        if (!element || !(element instanceof HTMLCanvasElement)) {
          throw new Error(`找不到ID为 ${options.canvas} 的画布元素`);
        }
        this.canvas = element;
      } else {
        this.canvas = options.canvas;
      }
    } else {
      this.canvas = document.createElement('canvas');
      document.body.appendChild(this.canvas);
    }

    // 创建Three.js渲染器
    this.renderer = new THREE.WebGLRenderer({
      canvas: this.canvas,
      antialias: options.antialias !== undefined ? options.antialias : true,
      alpha: options.alpha !== undefined ? options.alpha : false,
      depth: options.depth !== undefined ? options.depth : true,
      stencil: options.stencil !== undefined ? options.stencil : true,
      logarithmicDepthBuffer: options.logarithmicDepthBuffer !== undefined ? options.logarithmicDepthBuffer : false,
    });

    // 设置渲染器属性
    this.renderer.setClearColor(0x000000);
    this.renderer.setPixelRatio(options.pixelRatio || window.devicePixelRatio);

    // 设置阴影
    if (options.shadows !== undefined ? options.shadows : true) {
      this.renderer.shadowMap.enabled = true;
      this.renderer.shadowMap.type = options.shadowMapType || THREE.PCFSoftShadowMap;
    }

    // 设置色调映射
    if (options.toneMapping) {
      this.renderer.toneMapping = options.toneMapping;
      this.renderer.toneMappingExposure = options.toneMappingExposure || 1.0;
    }

    // 设置输出颜色空间
    if (options.outputColorSpace) {
      this.renderer.outputColorSpace = options.outputColorSpace;
    } else {
      this.renderer.outputColorSpace = THREE.SRGBColorSpace;
    }

    // 设置自动清除
    this.renderer.autoClear = options.autoClear !== undefined ? options.autoClear : true;

    // 设置自动调整大小
    this.autoResize = options.autoResize !== undefined ? options.autoResize : true;

    // 设置初始大小
    const width = options.width || this.canvas.clientWidth || 800;
    const height = options.height || this.canvas.clientHeight || 600;
    this.setSize(width, height);

    // 如果启用自动调整大小，添加窗口调整大小事件监听器
    if (this.autoResize) {
      this.resizeListener = this.handleResize.bind(this);
      window.addEventListener('resize', this.resizeListener);
    }
  }

  /**
   * 处理窗口调整大小事件
   */
  private handleResize(): void {
    if (!this.canvas.parentElement) {
      return;
    }

    const width = this.canvas.parentElement.clientWidth;
    const height = this.canvas.parentElement.clientHeight;

    this.setSize(width, height);
  }

  /**
   * 设置渲染器大小
   * @param width 宽度
   * @param height 高度
   * @param updateStyle 是否更新样式
   */
  public setSize(width: number, height: number, updateStyle: boolean = true): void {
    this.renderer.setSize(width, height, updateStyle);

    // 发出大小变更事件
    this.emit('resize', width, height);
  }

  /**
   * 获取渲染器大小
   * @returns 渲染器大小
   */
  public getSize(): { width: number; height: number } {
    const size = new THREE.Vector2();
    this.renderer.getSize(size);

    return {
      width: size.x,
      height: size.y,
    };
  }

  /**
   * 设置像素比例
   * @param pixelRatio 像素比例
   */
  public setPixelRatio(pixelRatio: number): void {
    this.renderer.setPixelRatio(pixelRatio);
  }

  /**
   * 获取像素比例
   * @returns 像素比例
   */
  public getPixelRatio(): number {
    return this.renderer.getPixelRatio();
  }

  /**
   * 设置清除颜色
   * @param color 颜色
   * @param alpha 透明度
   */
  public setClearColor(color: THREE.ColorRepresentation, alpha: number = 1): void {
    this.renderer.setClearColor(color, alpha);
  }

  /**
   * 获取清除颜色
   * @returns 清除颜色
   */
  public getClearColor(): THREE.Color {
    return this.renderer.getClearColor(new THREE.Color());
  }

  /**
   * 设置自动清除
   * @param autoClear 是否自动清除
   */
  public setAutoClear(autoClear: boolean): void {
    this.renderer.autoClear = autoClear;
  }

  /**
   * 是否自动清除
   * @returns 是否自动清除
   */
  public isAutoClear(): boolean {
    return this.renderer.autoClear;
  }

  /**
   * 清除渲染器
   */
  public clear(): void {
    this.renderer.clear();
  }

  /**
   * 渲染场景
   * @param scene 场景
   * @param camera 相机
   */
  public render(scene: Scene, camera: Camera): void {
    this.renderer.render(scene.getThreeScene(), camera.getThreeCamera());

    // 发出渲染事件
    this.emit('render', scene, camera);
  }

  /**
   * 获取Three.js渲染器
   * @returns Three.js渲染器
   */
  public getThreeRenderer(): THREE.WebGLRenderer {
    return this.renderer;
  }

  /**
   * 获取画布元素
   * @returns 画布元素
   */
  public getCanvas(): HTMLCanvasElement {
    return this.canvas;
  }

  /**
   * 设置自动调整大小
   * @param autoResize 是否自动调整大小
   */
  public setAutoResize(autoResize: boolean): void {
    if (this.autoResize === autoResize) {
      return;
    }

    this.autoResize = autoResize;

    if (autoResize) {
      // 添加窗口调整大小事件监听器
      if (!this.resizeListener) {
        this.resizeListener = this.handleResize.bind(this);
      }
      window.addEventListener('resize', this.resizeListener);
    } else {
      // 移除窗口调整大小事件监听器
      if (this.resizeListener) {
        window.removeEventListener('resize', this.resizeListener);
      }
    }
  }

  /**
   * 是否自动调整大小
   * @returns 是否自动调整大小
   */
  public isAutoResize(): boolean {
    return this.autoResize;
  }

  /**
   * 销毁渲染器
   */
  public dispose(): void {
    // 移除窗口调整大小事件监听器
    if (this.resizeListener) {
      window.removeEventListener('resize', this.resizeListener);
      this.resizeListener = null;
    }

    // 销毁Three.js渲染器
    (this.renderer as any).dispose();

    // 移除所有事件监听器
    this.removeAllListeners();
  }
}
