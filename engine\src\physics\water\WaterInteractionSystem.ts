/**
 * 水体交互系统
 * 用于处理水体与其他物体的交互效果
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import { PhysicsSystem } from '../PhysicsSystem';
import { WaterBodyComponent } from './WaterBodyComponent';
import { WaterPhysicsSystem, WaterPhysicsSystemEventType } from './WaterPhysicsSystem';
import { Debug } from '../../utils/Debug';
import { PerformanceMonitor } from '../../utils/PerformanceMonitor';
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 水体交互系统事件类型
 */
export enum WaterInteractionSystemEventType {
  /** 水花效果 */
  WATER_SPLASH = 'water_splash',
  /** 水波纹效果 */
  WATER_RIPPLE = 'water_ripple',
  /** 水滴效果 */
  WATER_DROPLET = 'water_droplet',
  /** 水流效果 */
  WATER_FLOW = 'water_flow',
  /** 水体分裂效果 */
  WATER_SPLITTING = 'water_splitting',
  /** 物体浮力效果 */
  OBJECT_BUOYANCY = 'object_buoyancy',
  /** 物体阻力效果 */
  OBJECT_DRAG = 'object_drag',
  /** 物体旋转阻力效果 */
  OBJECT_ROTATIONAL_DRAG = 'object_rotational_drag',
  /** 物体进入水体 */
  OBJECT_ENTER_WATER = 'object_enter_water',
  /** 物体离开水体 */
  OBJECT_EXIT_WATER = 'object_exit_water'
}

/**
 * 水体交互系统配置
 */
export interface WaterInteractionSystemConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否自动更新 */
  autoUpdate?: boolean;
  /** 更新频率 */
  updateFrequency?: number;

  // 基本效果开关
  /** 是否启用水花效果 */
  enableSplashEffect?: boolean;
  /** 是否启用水波纹效果 */
  enableRippleEffect?: boolean;
  /** 是否启用水滴效果 */
  enableDropletEffect?: boolean;
  /** 是否启用水流效果 */
  enableFlowEffect?: boolean;
  /** 是否启用水体分裂效果 */
  enableSplittingEffect?: boolean;
  /** 是否启用物体浮力效果 */
  enableBuoyancyEffect?: boolean;
  /** 是否启用物体阻力效果 */
  enableDragEffect?: boolean;

  // 高级浮力配置
  /** 是否使用体素浮力计算 */
  useVoxelBuoyancy?: boolean;
  /** 体素分辨率 */
  voxelResolution?: number;
  /** 是否启用浮力稳定化 */
  enableBuoyancyStabilization?: boolean;
  /** 浮力稳定化强度 */
  buoyancyStabilizationStrength?: number;

  // 高级阻力配置
  /** 是否使用方向性阻力 */
  useDirectionalDrag?: boolean;
  /** 是否启用湍流阻力 */
  enableTurbulenceDrag?: boolean;
  /** 是否启用旋转阻力 */
  enableRotationalDrag?: boolean;
  /** 旋转阻力强度 */
  rotationalDragStrength?: number;
  /** 旋转阻力系数 */
  rotationalDragCoefficient?: number;
  /** X方向阻力系数 */
  dragCoefficientX?: number;
  /** Y方向阻力系数 */
  dragCoefficientY?: number;
  /** Z方向阻力系数 */
  dragCoefficientZ?: number;

  // 高级水流配置
  /** 是否使用高级水流交互 */
  useAdvancedFlowInteraction?: boolean;
  /** 是否启用湍流水流 */
  enableTurbulentFlow?: boolean;
  /** X方向水流系数 */
  flowCoefficientX?: number;
  /** Y方向水流系数 */
  flowCoefficientY?: number;
  /** Z方向水流系数 */
  flowCoefficientZ?: number;

  // 调试和性能
  /** 是否启用性能监控 */
  enablePerformanceMonitoring?: boolean;
  /** 是否启用调试可视化 */
  enableDebugVisualization?: boolean;

  // 效果强度
  /** 水花效果强度 */
  splashEffectStrength?: number;
  /** 水波纹效果强度 */
  rippleEffectStrength?: number;
  /** 水滴效果强度 */
  dropletEffectStrength?: number;
  /** 水流效果强度 */
  flowEffectStrength?: number;
  /** 水体分裂效果强度 */
  splittingEffectStrength?: number;
  /** 物体浮力效果强度 */
  buoyancyEffectStrength?: number;
  /** 物体阻力效果强度 */
  dragEffectStrength?: number;
}

/**
 * 水体交互系统
 */
export class WaterInteractionSystem extends System {
  /** 配置 */
  private config: WaterInteractionSystemConfig;
  /** 水体物理系统 */
  private waterPhysicsSystem: WaterPhysicsSystem | null = null;
  /** 物理系统 */
  private physicsSystem: PhysicsSystem | null = null;
  /** 帧计数器 */
  private frameCount: number = 0;
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** 性能监视器 */
  private performanceMonitor: PerformanceMonitor = new PerformanceMonitor();
  /** 调试渲染器 */
  private debugRenderer: THREE.Object3D | null = null;
  /** 当前更新频率 */
  private currentUpdateFrequency: number = 1;
  /** 物体水体交互状态 */
  private objectWaterInteractionStates: Map<number, Map<number, {
    isInWater: boolean;
    submergedRatio: number;
    waterSurfaceHeight: number;
    lastPosition: THREE.Vector3;
    lastVelocity: THREE.Vector3;
    lastUpdateTime: number;
  }>> = new Map();

  /**
   * 构造函数
   * @param world 世界
   * @param config 配置
   */
  constructor(world: World, config: WaterInteractionSystemConfig = {}) {
    super('WaterInteractionSystem');

    // 设置配置
    this.config = {
      // 基本设置
      enabled: config.enabled !== undefined ? config.enabled : true,
      autoUpdate: config.autoUpdate !== undefined ? config.autoUpdate : true,
      updateFrequency: config.updateFrequency || 1,

      // 基本效果开关
      enableSplashEffect: config.enableSplashEffect !== undefined ? config.enableSplashEffect : true,
      enableRippleEffect: config.enableRippleEffect !== undefined ? config.enableRippleEffect : true,
      enableDropletEffect: config.enableDropletEffect !== undefined ? config.enableDropletEffect : true,
      enableFlowEffect: config.enableFlowEffect !== undefined ? config.enableFlowEffect : true,
      enableSplittingEffect: config.enableSplittingEffect !== undefined ? config.enableSplittingEffect : true,
      enableBuoyancyEffect: config.enableBuoyancyEffect !== undefined ? config.enableBuoyancyEffect : true,
      enableDragEffect: config.enableDragEffect !== undefined ? config.enableDragEffect : true,

      // 高级浮力配置
      useVoxelBuoyancy: config.useVoxelBuoyancy !== undefined ? config.useVoxelBuoyancy : false,
      voxelResolution: config.voxelResolution || 5,
      enableBuoyancyStabilization: config.enableBuoyancyStabilization !== undefined ? config.enableBuoyancyStabilization : true,
      buoyancyStabilizationStrength: config.buoyancyStabilizationStrength || 0.5,

      // 高级阻力配置
      useDirectionalDrag: config.useDirectionalDrag !== undefined ? config.useDirectionalDrag : false,
      enableTurbulenceDrag: config.enableTurbulenceDrag !== undefined ? config.enableTurbulenceDrag : false,
      enableRotationalDrag: config.enableRotationalDrag !== undefined ? config.enableRotationalDrag : false,
      rotationalDragStrength: config.rotationalDragStrength || 0.5,
      rotationalDragCoefficient: config.rotationalDragCoefficient || 0.4,
      dragCoefficientX: config.dragCoefficientX || 0.5,
      dragCoefficientY: config.dragCoefficientY || 0.8,
      dragCoefficientZ: config.dragCoefficientZ || 0.7,

      // 高级水流配置
      useAdvancedFlowInteraction: config.useAdvancedFlowInteraction !== undefined ? config.useAdvancedFlowInteraction : false,
      enableTurbulentFlow: config.enableTurbulentFlow !== undefined ? config.enableTurbulentFlow : false,
      flowCoefficientX: config.flowCoefficientX || 0.4,
      flowCoefficientY: config.flowCoefficientY || 0.7,
      flowCoefficientZ: config.flowCoefficientZ || 0.6,

      // 调试和性能
      enablePerformanceMonitoring: config.enablePerformanceMonitoring !== undefined ? config.enablePerformanceMonitoring : false,
      enableDebugVisualization: config.enableDebugVisualization !== undefined ? config.enableDebugVisualization : false,

      // 效果强度
      splashEffectStrength: config.splashEffectStrength || 1.0,
      rippleEffectStrength: config.rippleEffectStrength || 1.0,
      dropletEffectStrength: config.dropletEffectStrength || 1.0,
      flowEffectStrength: config.flowEffectStrength || 1.0,
      splittingEffectStrength: config.splittingEffectStrength || 1.0,
      buoyancyEffectStrength: config.buoyancyEffectStrength || 1.0,
      dragEffectStrength: config.dragEffectStrength || 1.0
    };

    // 设置当前更新频率
    this.currentUpdateFrequency = this.config.updateFrequency || 1;

    // 设置世界
    this.setWorld(world);

    // 获取物理系统和水体物理系统
    if (world) {
      this.physicsSystem = world.getSystem('PhysicsSystem') as PhysicsSystem;
      this.waterPhysicsSystem = world.getSystem('WaterPhysicsSystem') as WaterPhysicsSystem;
    }

    // 创建调试渲染器
    if (this.config.enableDebugVisualization) {
      this.createDebugRenderer();
    }

    // 注册水体物理系统事件
    this.registerWaterPhysicsSystemEvents();
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    // 如果没有物理系统或水体物理系统，尝试获取
    if (!this.physicsSystem && this.world) {
      this.physicsSystem = this.world.getSystem('PhysicsSystem') as PhysicsSystem;
    }
    if (!this.waterPhysicsSystem && this.world) {
      this.waterPhysicsSystem = this.world.getSystem('WaterPhysicsSystem') as WaterPhysicsSystem;
    }

    // 如果启用了性能监控，初始化性能监视器
    if (this.config.enablePerformanceMonitoring) {
      this.performanceMonitor.initialize();
    }
  }

  /**
   * 注册水体物理系统事件
   */
  private registerWaterPhysicsSystemEvents(): void {
    if (!this.waterPhysicsSystem) return;

    // 注册物体进入水体事件
    this.waterPhysicsSystem.on(WaterPhysicsSystemEventType.OBJECT_ENTER_WATER, (data: any) => {
      this.handleObjectEnterWater(data);
    });

    // 注册物体离开水体事件
    this.waterPhysicsSystem.on(WaterPhysicsSystemEventType.OBJECT_EXIT_WATER, (data: any) => {
      this.handleObjectExitWater(data);
    });

    // 注册水体碰撞事件
    this.waterPhysicsSystem.on(WaterPhysicsSystemEventType.WATER_COLLISION, (data: any) => {
      this.handleWaterCollision(data);
    });

    // 注册水流冲击事件
    this.waterPhysicsSystem.on(WaterPhysicsSystemEventType.WATER_FLOW_IMPACT, (data: any) => {
      this.handleWaterFlowImpact(data);
    });

    // 注册水体分裂事件
    this.waterPhysicsSystem.on(WaterPhysicsSystemEventType.WATER_SPLITTING, (data: any) => {
      this.handleWaterSplitting(data);
    });
  }

  /**
   * 创建调试渲染器
   */
  private createDebugRenderer(): void {
    // 创建调试渲染器
    this.debugRenderer = new THREE.Group();
    this.debugRenderer.name = 'WaterInteractionDebugRenderer';

    // 添加到场景
    if (this.world) {
      const scene = this.world.getScene();
      if (scene) {
        scene.add(this.debugRenderer);
      }
    }
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.config.enabled || !this.config.autoUpdate) {
      return;
    }

    // 按照当前更新频率更新
    this.frameCount++;
    if (this.frameCount % this.currentUpdateFrequency !== 0) {
      return;
    }

    // 如果启用了性能监控，开始计时
    if (this.config.enablePerformanceMonitoring) {
      this.performanceMonitor.start('waterInteractionUpdate');
    }

    // 更新水体交互
    this.updateWaterInteractions(deltaTime);

    // 如果启用了调试可视化，更新调试渲染器
    if (this.config.enableDebugVisualization && this.debugRenderer) {
      this.updateDebugRenderer();
    }

    // 如果启用了性能监控，结束计时
    if (this.config.enablePerformanceMonitoring) {
      this.performanceMonitor.end('waterInteractionUpdate');
    }
  }

  /**
   * 更新水体交互
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateWaterInteractions(deltaTime: number): void {
    // 如果没有物理系统或水体物理系统，则跳过
    if (!this.physicsSystem || !this.waterPhysicsSystem) return;

    // 获取所有物理体
    const bodies = this.physicsSystem.getBodies();
    if (!bodies || bodies.size === 0) return;

    // 获取所有水体
    const waterBodies = this.waterPhysicsSystem.getWaterBodies();
    if (!waterBodies || waterBodies.size === 0) return;

    // 遍历所有物理体
    for (const [entityId, physicsBody] of bodies) {
      // 获取物理体位置、速度和尺寸
      const bodyPosition = physicsBody.getPosition();
      const bodyVelocity = physicsBody.getVelocity();
      const bodySize = physicsBody.getSize();
      const bodyMass = physicsBody.getMass();

      // 如果物理体质量为0（静态物体），则跳过
      if (bodyMass === 0) continue;

      // 遍历所有水体
      for (const [waterEntityId, waterBody] of waterBodies) {
        // 更新物体与水体的交互
        this.updateObjectWaterInteraction(
          entityId,
          waterEntityId,
          physicsBody,
          waterBody,
          bodyPosition,
          bodyVelocity,
          bodySize,
          bodyMass,
          deltaTime
        );
      }
    }
  }

  /**
   * 更新物体与水体的交互
   * @param entityId 物体实体ID
   * @param waterEntityId 水体实体ID
   * @param physicsBody 物理体
   * @param waterBody 水体
   * @param bodyPosition 物体位置
   * @param bodyVelocity 物体速度
   * @param bodySize 物体尺寸
   * @param bodyMass 物体质量
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateObjectWaterInteraction(
    entityId: number,
    waterEntityId: number,
    physicsBody: any,
    waterBody: WaterBodyComponent,
    bodyPosition: THREE.Vector3,
    bodyVelocity: THREE.Vector3,
    bodySize: THREE.Vector3,
    bodyMass: number,
    deltaTime: number
  ): void {
    // 获取水体属性
    const waterPosition = waterBody.getPosition();
    const waterSize = waterBody.getSize();
    const heightMap = waterBody.getHeightMap();
    const resolution = waterBody.getResolution();

    // 检查物体是否在水体范围内
    const isInWaterX = bodyPosition.x + bodySize.x / 2 >= waterPosition.x - waterSize.width / 2 &&
                       bodyPosition.x - bodySize.x / 2 <= waterPosition.x + waterSize.width / 2;
    const isInWaterZ = bodyPosition.z + bodySize.z / 2 >= waterPosition.z - waterSize.depth / 2 &&
                       bodyPosition.z - bodySize.z / 2 <= waterPosition.z + waterSize.depth / 2;

    if (!isInWaterX || !isInWaterZ) {
      // 如果物体不在水体范围内，但之前在水体中，则触发离开水体事件
      this.handleObjectExitWaterIfNeeded(entityId, waterEntityId);
      return;
    }

    // 计算物体在水体中的位置
    const relativeX = (bodyPosition.x - (waterPosition.x - waterSize.width / 2)) / waterSize.width;
    const relativeZ = (bodyPosition.z - (waterPosition.z - waterSize.depth / 2)) / waterSize.depth;

    // 计算水面高度
    let waterHeight = waterPosition.y;

    // 如果有高度图，使用高度图计算水面高度
    if (heightMap && resolution > 0) {
      const gridX = Math.floor(relativeX * resolution);
      const gridZ = Math.floor(relativeZ * resolution);

      if (gridX >= 0 && gridX < resolution && gridZ >= 0 && gridZ < resolution) {
        const heightIndex = gridZ * resolution + gridX;
        waterHeight += heightMap[heightIndex];
      }
    }

    // 计算物体浸入水中的深度
    const submergedDepth = Math.max(0, waterHeight - (bodyPosition.y - bodySize.y / 2));
    const submergedRatio = Math.min(1, submergedDepth / bodySize.y);

    // 获取或创建物体水体交互状态
    let objectWaterStates = this.objectWaterInteractionStates.get(entityId);
    if (!objectWaterStates) {
      objectWaterStates = new Map();
      this.objectWaterInteractionStates.set(entityId, objectWaterStates);
    }

    let state = objectWaterStates.get(waterEntityId);
    if (!state) {
      state = {
        isInWater: false,
        submergedRatio: 0,
        waterSurfaceHeight: waterHeight,
        lastPosition: bodyPosition.clone(),
        lastVelocity: bodyVelocity.clone(),
        lastUpdateTime: 0
      };
      objectWaterStates.set(waterEntityId, state);
    }

    // 检查物体是否刚刚进入水中
    const wasInWater = state.isInWater;
    const isInWater = submergedRatio > 0;

    if (!wasInWater && isInWater) {
      // 物体刚刚进入水中
      this.handleObjectEnterWater({
        entityId,
        waterEntityId,
        position: bodyPosition.clone(),
        velocity: bodyVelocity.clone(),
        size: bodySize.clone(),
        mass: bodyMass,
        waterBody,
        waterHeight,
        submergedRatio
      });
    } else if (wasInWater && !isInWater) {
      // 物体刚刚离开水中
      this.handleObjectExitWater({
        entityId,
        waterEntityId,
        position: bodyPosition.clone(),
        velocity: bodyVelocity.clone(),
        size: bodySize.clone(),
        mass: bodyMass,
        waterBody,
        waterHeight
      });
    } else if (isInWater) {
      // 物体在水中，更新交互效果
      this.updateInWaterEffects(
        entityId,
        waterEntityId,
        physicsBody,
        waterBody,
        bodyPosition,
        bodyVelocity,
        bodySize,
        bodyMass,
        waterHeight,
        submergedRatio,
        state,
        deltaTime
      );
    }

    // 更新状态
    state.isInWater = isInWater;
    state.submergedRatio = submergedRatio;
    state.waterSurfaceHeight = waterHeight;
    state.lastPosition.copy(bodyPosition);
    state.lastVelocity.copy(bodyVelocity);
    state.lastUpdateTime += deltaTime;
  }

  /**
   * 更新物体在水中的效果
   * @param entityId 物体实体ID
   * @param waterEntityId 水体实体ID
   * @param physicsBody 物理体
   * @param waterBody 水体
   * @param bodyPosition 物体位置
   * @param bodyVelocity 物体速度
   * @param bodySize 物体尺寸
   * @param bodyMass 物体质量
   * @param waterHeight 水面高度
   * @param submergedRatio 浸入比例
   * @param state 交互状态
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateInWaterEffects(
    entityId: number,
    waterEntityId: number,
    physicsBody: any,
    waterBody: WaterBodyComponent,
    bodyPosition: THREE.Vector3,
    bodyVelocity: THREE.Vector3,
    bodySize: THREE.Vector3,
    bodyMass: number,
    waterHeight: number,
    submergedRatio: number,
    state: any,
    deltaTime: number
  ): void {
    // 应用浮力效果
    if (this.config.enableBuoyancyEffect) {
      this.applyBuoyancyEffect(
        physicsBody,
        waterBody,
        bodyPosition,
        bodySize,
        bodyMass,
        waterHeight,
        submergedRatio
      );
    }

    // 应用阻力效果
    if (this.config.enableDragEffect) {
      this.applyDragEffect(
        physicsBody,
        waterBody,
        bodyVelocity,
        bodySize,
        submergedRatio
      );
    }

    // 应用水流效果
    if (this.config.enableFlowEffect) {
      this.applyFlowEffect(
        physicsBody,
        waterBody,
        bodyPosition,
        bodySize,
        bodyMass,
        submergedRatio
      );
    }

    // 检查是否需要创建水花效果
    if (this.config.enableSplashEffect) {
      this.checkAndCreateSplashEffect(
        entityId,
        waterEntityId,
        physicsBody,
        waterBody,
        bodyPosition,
        bodyVelocity,
        bodySize,
        waterHeight,
        state
      );
    }

    // 检查是否需要创建水波纹效果
    if (this.config.enableRippleEffect) {
      this.checkAndCreateRippleEffect(
        entityId,
        waterEntityId,
        physicsBody,
        waterBody,
        bodyPosition,
        bodyVelocity,
        bodySize,
        waterHeight,
        state
      );
    }

    // 检查是否需要创建水滴效果
    if (this.config.enableDropletEffect) {
      this.checkAndCreateDropletEffect(
        entityId,
        waterEntityId,
        physicsBody,
        waterBody,
        bodyPosition,
        bodyVelocity,
        bodySize,
        waterHeight,
        submergedRatio,
        state
      );
    }

    // 检查是否需要创建水体分裂效果
    if (this.config.enableSplittingEffect) {
      this.checkAndCreateSplittingEffect(
        entityId,
        waterEntityId,
        physicsBody,
        waterBody,
        bodyPosition,
        bodyVelocity,
        bodySize,
        waterHeight,
        state
      );
    }
  }

  /**
   * 应用浮力效果
   * @param physicsBody 物理体
   * @param waterBody 水体
   * @param bodyPosition 物体位置
   * @param bodySize 物体尺寸
   * @param bodyMass 物体质量
   * @param waterHeight 水面高度
   * @param submergedRatio 浸入比例
   */
  private applyBuoyancyEffect(
    physicsBody: any,
    waterBody: WaterBodyComponent,
    bodyPosition: THREE.Vector3,
    bodySize: THREE.Vector3,
    bodyMass: number,
    waterHeight: number,
    submergedRatio: number
  ): void {
    // 获取水体属性
    const waterDensity = waterBody.getDensity();
    const gravity = this.physicsSystem?.getGravity() || new THREE.Vector3(0, -9.8, 0);

    // 获取物体形状类型
    const shapeType = physicsBody.getShapeType();
    let buoyancyForce: THREE.Vector3;
    let buoyancyTorque: THREE.Vector3 = new THREE.Vector3();

    // 根据形状类型使用不同的浮力计算方法
    if (this.config.useVoxelBuoyancy && (shapeType === 'BOX' || shapeType === 'CONVEX' || shapeType === 'MESH')) {
      // 使用体素浮力计算
      const result = this.calculateVoxelBuoyancy(
        physicsBody,
        waterBody,
        bodyPosition,
        bodySize,
        bodyMass,
        waterHeight
      );
      buoyancyForce = result.force;
      buoyancyTorque = result.torque;
    } else {
      // 使用简单浮力计算
      const volume = bodySize.x * bodySize.y * bodySize.z;
      const submergedVolume = volume * submergedRatio;
      buoyancyForce = new THREE.Vector3(
        0,
        -gravity.y * waterDensity * submergedVolume * this.config.buoyancyEffectStrength!,
        0
      );
    }

    // 应用浮力
    physicsBody.applyForce(buoyancyForce);

    // 应用浮力扭矩（如果有）
    if (buoyancyTorque.lengthSq() > 0.0001) {
      physicsBody.applyTorque(buoyancyTorque);
    }

    // 应用稳定性控制（防止物体在水面上不自然抖动）
    if (this.config.enableBuoyancyStabilization && submergedRatio > 0.01 && submergedRatio < 0.99) {
      this.applyBuoyancyStabilization(physicsBody, waterBody, submergedRatio);
    }

    // 发射浮力事件
    this.eventEmitter.emit(WaterInteractionSystemEventType.OBJECT_BUOYANCY, {
      physicsBody,
      waterBody,
      position: bodyPosition.clone(),
      size: bodySize.clone(),
      mass: bodyMass,
      waterHeight,
      submergedRatio,
      buoyancyForce: buoyancyForce.clone(),
      buoyancyTorque: buoyancyTorque.clone()
    });
  }

  /**
   * 计算体素浮力
   * @param physicsBody 物理体
   * @param waterBody 水体
   * @param bodyPosition 物体位置
   * @param bodySize 物体尺寸
   * @param bodyMass 物体质量
   * @param waterHeight 水面高度
   * @returns 浮力和扭矩
   */
  private calculateVoxelBuoyancy(
    physicsBody: any,
    waterBody: WaterBodyComponent,
    bodyPosition: THREE.Vector3,
    bodySize: THREE.Vector3,
    bodyMass: number,
    waterHeight: number
  ): { force: THREE.Vector3; torque: THREE.Vector3 } {
    // 获取水体属性
    const waterDensity = waterBody.getDensity();
    const gravity = this.physicsSystem?.getGravity() || new THREE.Vector3(0, -9.8, 0);

    // 获取物体的旋转信息
    const rotation = physicsBody.getRotation();

    // 创建体素网格
    const voxelCount = this.config.voxelResolution || 5; // 每个维度的体素数量
    const voxelSize = new THREE.Vector3(
      bodySize.x / voxelCount,
      bodySize.y / voxelCount,
      bodySize.z / voxelCount
    );
    const voxelVolume = voxelSize.x * voxelSize.y * voxelSize.z;

    // 计算物体的局部坐标系中心点
    const localCenter = new THREE.Vector3(0, 0, 0);

    // 初始化总浮力和扭矩
    const totalForce = new THREE.Vector3(0, 0, 0);
    const totalTorque = new THREE.Vector3(0, 0, 0);

    // 遍历所有体素
    for (let x = 0; x < voxelCount; x++) {
      for (let y = 0; y < voxelCount; y++) {
        for (let z = 0; z < voxelCount; z++) {
          // 计算体素在局部坐标系中的位置
          const localPos = new THREE.Vector3(
            (x + 0.5) * voxelSize.x - bodySize.x / 2,
            (y + 0.5) * voxelSize.y - bodySize.y / 2,
            (z + 0.5) * voxelSize.z - bodySize.z / 2
          );

          // 将局部坐标转换为世界坐标
          const worldPos = new THREE.Vector3().copy(localPos);
          worldPos.applyQuaternion(rotation);
          worldPos.add(bodyPosition);

          // 检查体素是否在水下
          if (worldPos.y < waterHeight) {
            // 计算体素浮力
            const voxelForce = new THREE.Vector3(
              0,
              -gravity.y * waterDensity * voxelVolume * this.config.buoyancyEffectStrength!,
              0
            );

            // 将力从世界坐标转换回局部坐标
            const localForce = new THREE.Vector3().copy(voxelForce);

            // 计算扭矩（力矩 = 位置向量 × 力向量）
            const torque = new THREE.Vector3().crossVectors(localPos, localForce);

            // 累加力和扭矩
            totalForce.add(voxelForce);
            totalTorque.add(torque);
          }
        }
      }
    }

    // 将扭矩从局部坐标转换为世界坐标
    const worldTorque = new THREE.Vector3().copy(totalTorque);
    worldTorque.applyQuaternion(rotation);

    return { force: totalForce, torque: worldTorque };
  }

  /**
   * 应用浮力稳定化
   * @param physicsBody 物理体
   * @param waterBody 水体
   * @param submergedRatio 浸入比例
   */
  private applyBuoyancyStabilization(
    physicsBody: any,
    waterBody: WaterBodyComponent,
    submergedRatio: number
  ): void {
    // 获取物体的角速度
    const angularVelocity = physicsBody.getAngularVelocity();

    // 计算稳定化系数（浸入比例接近0.5时最强）
    const stabilizationFactor = (1.0 - Math.abs(submergedRatio - 0.5) * 2.0) *
                               this.config.buoyancyStabilizationStrength!;

    // 计算阻尼力矩（与角速度方向相反）
    const dampingTorque = new THREE.Vector3(
      -angularVelocity.x * stabilizationFactor,
      -angularVelocity.y * stabilizationFactor,
      -angularVelocity.z * stabilizationFactor
    );

    // 应用阻尼力矩
    physicsBody.applyTorque(dampingTorque);
  }

  /**
   * 应用阻力效果
   * @param physicsBody 物理体
   * @param waterBody 水体
   * @param bodyVelocity 物体速度
   * @param bodySize 物体尺寸
   * @param submergedRatio 浸入比例
   */
  private applyDragEffect(
    physicsBody: any,
    waterBody: WaterBodyComponent,
    bodyVelocity: THREE.Vector3,
    bodySize: THREE.Vector3,
    submergedRatio: number
  ): void {
    // 如果速度太小，忽略阻力
    const velocitySquared = bodyVelocity.lengthSq();
    if (velocitySquared < 0.0001) return;

    // 获取水体属性
    const waterViscosity = waterBody.getViscosity();

    // 获取物体的旋转信息和角速度
    const rotation = physicsBody.getRotation();
    const angularVelocity = physicsBody.getAngularVelocity();

    // 计算线性阻力
    if (this.config.useDirectionalDrag) {
      // 使用方向性阻力计算
      this.applyDirectionalDrag(
        physicsBody,
        waterBody,
        bodyVelocity,
        bodySize,
        rotation,
        submergedRatio
      );
    } else {
      // 使用简单阻力计算
      const dragCoefficient = 0.5; // 基础阻力系数
      const area = Math.max(bodySize.x * bodySize.z, bodySize.x * bodySize.y, bodySize.y * bodySize.z);

      // 如果启用湍流阻力，添加湍流因子
      let turbulenceFactor = 1.0;
      if (this.config.enableTurbulenceDrag) {
        // 获取水体湍流强度
        const turbulenceIntensity = waterBody.getTurbulenceIntensity() || 0;
        // 湍流因子随速度和湍流强度增加而增加
        turbulenceFactor = 1.0 + turbulenceIntensity * Math.min(1.0, bodyVelocity.length() / 10.0);
      }

      const velocityDirection = bodyVelocity.clone().normalize();
      const dragMagnitude = dragCoefficient * waterViscosity * area * velocitySquared *
                           submergedRatio * this.config.dragEffectStrength! * turbulenceFactor;

      const dragForce = velocityDirection.multiplyScalar(-dragMagnitude);

      // 应用线性阻力
      physicsBody.applyForce(dragForce);

      // 发射阻力事件
      this.eventEmitter.emit(WaterInteractionSystemEventType.OBJECT_DRAG, {
        physicsBody,
        waterBody,
        velocity: bodyVelocity.clone(),
        size: bodySize.clone(),
        submergedRatio,
        dragForce: dragForce.clone(),
        turbulenceFactor
      });
    }

    // 计算旋转阻力
    if (this.config.enableRotationalDrag && angularVelocity.lengthSq() > 0.0001) {
      this.applyRotationalDrag(
        physicsBody,
        waterBody,
        angularVelocity,
        bodySize,
        submergedRatio
      );
    }
  }

  /**
   * 应用方向性阻力
   * @param physicsBody 物理体
   * @param waterBody 水体
   * @param bodyVelocity 物体速度
   * @param bodySize 物体尺寸
   * @param rotation 物体旋转
   * @param submergedRatio 浸入比例
   */
  private applyDirectionalDrag(
    physicsBody: any,
    waterBody: WaterBodyComponent,
    bodyVelocity: THREE.Vector3,
    bodySize: THREE.Vector3,
    rotation: THREE.Quaternion,
    submergedRatio: number
  ): void {
    // 获取水体属性
    const waterViscosity = waterBody.getViscosity();

    // 将速度从世界坐标转换到物体的局部坐标系
    const localVelocity = new THREE.Vector3().copy(bodyVelocity);
    const inverseRotation = rotation.clone().invert();
    localVelocity.applyQuaternion(inverseRotation);

    // 计算各个方向的阻力系数（基于物体形状）
    // 流线型物体在前进方向的阻力较小，侧向阻力较大
    const dragCoefficients = {
      x: this.config.dragCoefficientX || 0.5, // 前后方向
      y: this.config.dragCoefficientY || 0.8, // 上下方向
      z: this.config.dragCoefficientZ || 0.7  // 左右方向
    };

    // 计算各个方向的投影面积
    const areas = {
      x: bodySize.y * bodySize.z, // 前后方向的投影面积
      y: bodySize.x * bodySize.z, // 上下方向的投影面积
      z: bodySize.x * bodySize.y  // 左右方向的投影面积
    };

    // 计算各个方向的阻力
    const dragForceLocal = new THREE.Vector3(
      -Math.sign(localVelocity.x) * dragCoefficients.x * areas.x * localVelocity.x * localVelocity.x,
      -Math.sign(localVelocity.y) * dragCoefficients.y * areas.y * localVelocity.y * localVelocity.y,
      -Math.sign(localVelocity.z) * dragCoefficients.z * areas.z * localVelocity.z * localVelocity.z
    );

    // 应用水体粘度和浸入比例
    dragForceLocal.multiplyScalar(waterViscosity * submergedRatio * this.config.dragEffectStrength!);

    // 如果启用湍流阻力，添加湍流因子
    if (this.config.enableTurbulenceDrag) {
      // 获取水体湍流强度
      const turbulenceIntensity = waterBody.getTurbulenceIntensity() || 0;
      // 湍流因子随速度和湍流强度增加而增加
      const turbulenceFactor = 1.0 + turbulenceIntensity * Math.min(1.0, bodyVelocity.length() / 10.0);
      dragForceLocal.multiplyScalar(turbulenceFactor);
    }

    // 将阻力从局部坐标转换回世界坐标
    const dragForceWorld = new THREE.Vector3().copy(dragForceLocal);
    dragForceWorld.applyQuaternion(rotation);

    // 应用阻力
    physicsBody.applyForce(dragForceWorld);

    // 发射阻力事件
    this.eventEmitter.emit(WaterInteractionSystemEventType.OBJECT_DRAG, {
      physicsBody,
      waterBody,
      velocity: bodyVelocity.clone(),
      size: bodySize.clone(),
      submergedRatio,
      dragForce: dragForceWorld.clone(),
      directional: true
    });
  }

  /**
   * 应用旋转阻力
   * @param physicsBody 物理体
   * @param waterBody 水体
   * @param angularVelocity 角速度
   * @param bodySize 物体尺寸
   * @param submergedRatio 浸入比例
   */
  private applyRotationalDrag(
    physicsBody: any,
    waterBody: WaterBodyComponent,
    angularVelocity: THREE.Vector3,
    bodySize: THREE.Vector3,
    submergedRatio: number
  ): void {
    // 获取水体属性
    const waterViscosity = waterBody.getViscosity();

    // 计算物体的特征尺寸（用于旋转阻力计算）
    const characteristicLength = Math.max(bodySize.x, bodySize.y, bodySize.z);

    // 计算旋转阻力系数
    const rotationalDragCoefficient = this.config.rotationalDragCoefficient || 0.4;

    // 计算旋转阻力力矩
    const rotationalDragTorque = new THREE.Vector3(
      -angularVelocity.x * Math.abs(angularVelocity.x) * rotationalDragCoefficient,
      -angularVelocity.y * Math.abs(angularVelocity.y) * rotationalDragCoefficient,
      -angularVelocity.z * Math.abs(angularVelocity.z) * rotationalDragCoefficient
    );

    // 应用特征尺寸、水体粘度和浸入比例
    const scaleFactor = characteristicLength * characteristicLength * characteristicLength *
                       waterViscosity * submergedRatio * this.config.rotationalDragStrength!;
    rotationalDragTorque.multiplyScalar(scaleFactor);

    // 应用旋转阻力力矩
    physicsBody.applyTorque(rotationalDragTorque);

    // 发射旋转阻力事件
    this.eventEmitter.emit(WaterInteractionSystemEventType.OBJECT_ROTATIONAL_DRAG, {
      physicsBody,
      waterBody,
      angularVelocity: angularVelocity.clone(),
      size: bodySize.clone(),
      submergedRatio,
      rotationalDragTorque: rotationalDragTorque.clone()
    });
  }

  /**
   * 应用水流效果
   * @param physicsBody 物理体
   * @param waterBody 水体
   * @param bodyPosition 物体位置
   * @param bodySize 物体尺寸
   * @param bodyMass 物体质量
   * @param submergedRatio 浸入比例
   */
  private applyFlowEffect(
    physicsBody: any,
    waterBody: WaterBodyComponent,
    bodyPosition: THREE.Vector3,
    bodySize: THREE.Vector3,
    bodyMass: number,
    submergedRatio: number
  ): void {
    // 获取水流方向和速度
    const flowDirection = waterBody.getFlowDirection();
    const flowSpeed = waterBody.getFlowSpeed();

    if (flowSpeed < 0.001) return; // 水流速度太小，忽略水流效果

    // 获取物体的旋转信息和速度
    const rotation = physicsBody.getRotation();
    const bodyVelocity = physicsBody.getVelocity();

    // 计算物体相对于水流的速度
    const relativeVelocity = new THREE.Vector3(
      bodyVelocity.x - flowDirection.x * flowSpeed,
      bodyVelocity.y - flowDirection.y * flowSpeed,
      bodyVelocity.z - flowDirection.z * flowSpeed
    );

    // 计算水流力和扭矩
    let flowForce: THREE.Vector3;
    let flowTorque: THREE.Vector3 = new THREE.Vector3();

    if (this.config.useAdvancedFlowInteraction) {
      // 使用高级水流交互计算
      const result = this.calculateAdvancedFlowInteraction(
        physicsBody,
        waterBody,
        bodyPosition,
        bodySize,
        rotation,
        flowDirection,
        flowSpeed,
        relativeVelocity,
        submergedRatio
      );
      flowForce = result.force;
      flowTorque = result.torque;
    } else {
      // 使用简单水流力计算
      const area = Math.max(bodySize.x * bodySize.z, bodySize.x * bodySize.y, bodySize.y * bodySize.z);

      // 计算水流动力系数（考虑湍流）
      let flowCoefficient = 0.5; // 基础水流动力系数

      // 如果启用湍流水流，添加湍流因子
      if (this.config.enableTurbulentFlow) {
        // 获取水体湍流强度
        const turbulenceIntensity = waterBody.getTurbulenceIntensity() || 0;
        // 湍流因子随水流速度和湍流强度增加而增加
        flowCoefficient += turbulenceIntensity * Math.min(1.0, flowSpeed / 5.0) * 0.3;
      }

      // 计算水流力
      flowForce = new THREE.Vector3(
        flowDirection.x * flowSpeed * flowSpeed * area * flowCoefficient * submergedRatio * this.config.flowEffectStrength!,
        flowDirection.y * flowSpeed * flowSpeed * area * flowCoefficient * submergedRatio * this.config.flowEffectStrength!,
        flowDirection.z * flowSpeed * flowSpeed * area * flowCoefficient * submergedRatio * this.config.flowEffectStrength!
      );
    }

    // 应用水流力
    physicsBody.applyForce(flowForce);

    // 应用水流扭矩（如果有）
    if (flowTorque.lengthSq() > 0.0001) {
      physicsBody.applyTorque(flowTorque);
    }

    // 发射水流效果事件
    this.eventEmitter.emit(WaterInteractionSystemEventType.WATER_FLOW, {
      physicsBody,
      waterBody,
      position: bodyPosition.clone(),
      size: bodySize.clone(),
      mass: bodyMass,
      submergedRatio,
      flowDirection: flowDirection.clone(),
      flowSpeed,
      flowForce: flowForce.clone(),
      flowTorque: flowTorque.clone()
    });
  }

  /**
   * 计算高级水流交互
   * @param physicsBody 物理体
   * @param waterBody 水体
   * @param bodyPosition 物体位置
   * @param bodySize 物体尺寸
   * @param rotation 物体旋转
   * @param flowDirection 水流方向
   * @param flowSpeed 水流速度
   * @param relativeVelocity 相对速度
   * @param submergedRatio 浸入比例
   * @returns 水流力和扭矩
   */
  private calculateAdvancedFlowInteraction(
    physicsBody: any,
    waterBody: WaterBodyComponent,
    bodyPosition: THREE.Vector3,
    bodySize: THREE.Vector3,
    rotation: THREE.Quaternion,
    flowDirection: THREE.Vector3,
    flowSpeed: number,
    relativeVelocity: THREE.Vector3,
    submergedRatio: number
  ): { force: THREE.Vector3; torque: THREE.Vector3 } {
    // 将水流方向从世界坐标转换到物体的局部坐标系
    const localFlowDirection = new THREE.Vector3().copy(flowDirection);
    const inverseRotation = rotation.clone().invert();
    localFlowDirection.applyQuaternion(inverseRotation);
    localFlowDirection.normalize();

    // 计算各个方向的流体动力系数（基于物体形状和流线型程度）
    const flowCoefficients = {
      x: this.config.flowCoefficientX || 0.4, // 前后方向
      y: this.config.flowCoefficientY || 0.7, // 上下方向
      z: this.config.flowCoefficientZ || 0.6  // 左右方向
    };

    // 计算各个方向的投影面积
    const areas = {
      x: bodySize.y * bodySize.z, // 前后方向的投影面积
      y: bodySize.x * bodySize.z, // 上下方向的投影面积
      z: bodySize.x * bodySize.y  // 左右方向的投影面积
    };

    // 计算局部坐标系中的水流力
    const localFlowForce = new THREE.Vector3(
      Math.abs(localFlowDirection.x) * flowCoefficients.x * areas.x,
      Math.abs(localFlowDirection.y) * flowCoefficients.y * areas.y,
      Math.abs(localFlowDirection.z) * flowCoefficients.z * areas.z
    );

    // 应用水流速度和方向
    localFlowForce.x *= Math.sign(localFlowDirection.x) * flowSpeed * flowSpeed;
    localFlowForce.y *= Math.sign(localFlowDirection.y) * flowSpeed * flowSpeed;
    localFlowForce.z *= Math.sign(localFlowDirection.z) * flowSpeed * flowSpeed;

    // 如果启用湍流水流，添加湍流因子
    if (this.config.enableTurbulentFlow) {
      // 获取水体湍流强度
      const turbulenceIntensity = waterBody.getTurbulenceIntensity() || 0;
      // 湍流因子随水流速度和湍流强度增加而增加
      const turbulenceFactor = 1.0 + turbulenceIntensity * Math.min(1.0, flowSpeed / 5.0) * 0.5;
      localFlowForce.multiplyScalar(turbulenceFactor);
    }

    // 应用浸入比例和效果强度
    localFlowForce.multiplyScalar(submergedRatio * this.config.flowEffectStrength!);

    // 计算水流扭矩（力矩 = 位置向量 × 力向量）
    // 使用物体的中心作为参考点，计算水流作用点的偏移
    const flowCenterOffset = new THREE.Vector3(
      0, // 假设水流作用在物体中心的x轴上
      -bodySize.y * 0.25 * submergedRatio, // 水流作用点略低于物体中心
      0  // 假设水流作用在物体中心的z轴上
    );

    // 计算局部坐标系中的扭矩
    const localTorque = new THREE.Vector3().crossVectors(flowCenterOffset, localFlowForce);

    // 将力和扭矩从局部坐标转换回世界坐标
    const worldFlowForce = new THREE.Vector3().copy(localFlowForce);
    worldFlowForce.applyQuaternion(rotation);

    const worldTorque = new THREE.Vector3().copy(localTorque);
    worldTorque.applyQuaternion(rotation);

    return { force: worldFlowForce, torque: worldTorque };
  }

  /**
   * 检查并创建水花效果
   * @param entityId 物体实体ID
   * @param waterEntityId 水体实体ID
   * @param physicsBody 物理体
   * @param waterBody 水体
   * @param bodyPosition 物体位置
   * @param bodyVelocity 物体速度
   * @param bodySize 物体尺寸
   * @param waterHeight 水面高度
   * @param state 交互状态
   */
  private checkAndCreateSplashEffect(
    entityId: number,
    waterEntityId: number,
    physicsBody: any,
    waterBody: WaterBodyComponent,
    bodyPosition: THREE.Vector3,
    bodyVelocity: THREE.Vector3,
    bodySize: THREE.Vector3,
    waterHeight: number,
    state: any
  ): void {
    // 检查物体是否穿过水面
    const isCrossingWaterSurface =
      (bodyPosition.y - bodySize.y / 2 <= waterHeight && state.lastPosition.y - bodySize.y / 2 > waterHeight) ||
      (bodyPosition.y + bodySize.y / 2 >= waterHeight && state.lastPosition.y + bodySize.y / 2 < waterHeight);

    if (!isCrossingWaterSurface) return;

    // 计算穿过水面的速度
    const verticalVelocity = Math.abs(bodyVelocity.y);

    // 如果速度太小，不创建水花效果
    if (verticalVelocity < 0.5) return;

    // 计算水花效果强度
    const splashStrength = Math.min(1.0, verticalVelocity / 10.0) * this.config.splashEffectStrength!;

    // 计算水花效果位置
    const splashPosition = new THREE.Vector3(
      bodyPosition.x,
      waterHeight,
      bodyPosition.z
    );

    // 发射水花效果事件
    this.eventEmitter.emit(WaterInteractionSystemEventType.WATER_SPLASH, {
      entityId,
      waterEntityId,
      position: splashPosition,
      velocity: bodyVelocity.clone(),
      size: bodySize.clone(),
      strength: splashStrength,
      waterBody
    });

    // 如果水体物理系统存在，通知水体物理系统创建水花效果
    if (this.waterPhysicsSystem) {
      this.waterPhysicsSystem.createSplashEffect(
        waterEntityId,
        splashPosition,
        splashStrength,
        bodySize
      );
    }
  }

  /**
   * 检查并创建水波纹效果
   * @param entityId 物体实体ID
   * @param waterEntityId 水体实体ID
   * @param physicsBody 物理体
   * @param waterBody 水体
   * @param bodyPosition 物体位置
   * @param bodyVelocity 物体速度
   * @param bodySize 物体尺寸
   * @param waterHeight 水面高度
   * @param state 交互状态
   */
  private checkAndCreateRippleEffect(
    entityId: number,
    waterEntityId: number,
    physicsBody: any,
    waterBody: WaterBodyComponent,
    bodyPosition: THREE.Vector3,
    bodyVelocity: THREE.Vector3,
    bodySize: THREE.Vector3,
    waterHeight: number,
    state: any
  ): void {
    // 检查物体是否在水面附近
    const isNearWaterSurface = Math.abs(bodyPosition.y - waterHeight) < bodySize.y / 2;

    if (!isNearWaterSurface) return;

    // 计算水平速度
    const horizontalVelocity = new THREE.Vector2(bodyVelocity.x, bodyVelocity.z).length();

    // 如果速度太小，不创建水波纹效果
    if (horizontalVelocity < 0.3) return;

    // 计算水波纹效果强度
    const rippleStrength = Math.min(1.0, horizontalVelocity / 5.0) * this.config.rippleEffectStrength!;

    // 计算水波纹效果位置
    const ripplePosition = new THREE.Vector3(
      bodyPosition.x,
      waterHeight,
      bodyPosition.z
    );

    // 计算水波纹效果方向
    const rippleDirection = new THREE.Vector2(bodyVelocity.x, bodyVelocity.z).normalize();

    // 发射水波纹效果事件
    this.eventEmitter.emit(WaterInteractionSystemEventType.WATER_RIPPLE, {
      entityId,
      waterEntityId,
      position: ripplePosition,
      velocity: bodyVelocity.clone(),
      direction: rippleDirection,
      size: bodySize.clone(),
      strength: rippleStrength,
      waterBody
    });

    // 如果水体物理系统存在，通知水体物理系统创建水波纹效果
    if (this.waterPhysicsSystem) {
      this.waterPhysicsSystem.createRippleEffect(
        waterEntityId,
        ripplePosition,
        rippleStrength,
        new THREE.Vector3(rippleDirection.x, 0, rippleDirection.y)
      );
    }
  }

  /**
   * 检查并创建水滴效果
   * @param entityId 物体实体ID
   * @param waterEntityId 水体实体ID
   * @param physicsBody 物理体
   * @param waterBody 水体
   * @param bodyPosition 物体位置
   * @param bodyVelocity 物体速度
   * @param bodySize 物体尺寸
   * @param waterHeight 水面高度
   * @param submergedRatio 浸入比例
   * @param state 交互状态
   */
  private checkAndCreateDropletEffect(
    entityId: number,
    waterEntityId: number,
    physicsBody: any,
    waterBody: WaterBodyComponent,
    bodyPosition: THREE.Vector3,
    bodyVelocity: THREE.Vector3,
    bodySize: THREE.Vector3,
    waterHeight: number,
    submergedRatio: number,
    state: any
  ): void {
    // 检查物体是否刚刚离开水面
    const isLeavingWaterSurface =
      state.isInWater &&
      bodyPosition.y - bodySize.y / 2 > waterHeight &&
      state.lastPosition.y - bodySize.y / 2 <= waterHeight;

    if (!isLeavingWaterSurface) return;

    // 计算垂直速度
    const verticalVelocity = Math.max(0, bodyVelocity.y);

    // 如果速度太小，不创建水滴效果
    if (verticalVelocity < 0.5) return;

    // 计算水滴效果强度
    const dropletStrength = Math.min(1.0, verticalVelocity / 5.0) * this.config.dropletEffectStrength!;

    // 计算水滴效果位置
    const dropletPosition = new THREE.Vector3(
      bodyPosition.x,
      waterHeight,
      bodyPosition.z
    );

    // 发射水滴效果事件
    this.eventEmitter.emit(WaterInteractionSystemEventType.WATER_DROPLET, {
      entityId,
      waterEntityId,
      position: dropletPosition,
      velocity: bodyVelocity.clone(),
      size: bodySize.clone(),
      strength: dropletStrength,
      waterBody
    });

    // 如果水体物理系统存在，通知水体物理系统创建水滴效果
    if (this.waterPhysicsSystem) {
      this.waterPhysicsSystem.createDropletEffect(
        waterEntityId,
        dropletPosition,
        dropletStrength,
        bodyVelocity
      );
    }
  }

  /**
   * 检查并创建水体分裂效果
   * @param entityId 物体实体ID
   * @param waterEntityId 水体实体ID
   * @param physicsBody 物理体
   * @param waterBody 水体
   * @param bodyPosition 物体位置
   * @param bodyVelocity 物体速度
   * @param bodySize 物体尺寸
   * @param waterHeight 水面高度
   * @param state 交互状态
   */
  private checkAndCreateSplittingEffect(
    entityId: number,
    waterEntityId: number,
    physicsBody: any,
    waterBody: WaterBodyComponent,
    bodyPosition: THREE.Vector3,
    bodyVelocity: THREE.Vector3,
    bodySize: THREE.Vector3,
    waterHeight: number,
    state: any
  ): void {
    // 检查物体是否在水面附近
    const isNearWaterSurface = Math.abs(bodyPosition.y - waterHeight) < bodySize.y / 2;

    if (!isNearWaterSurface) return;

    // 计算水平速度
    const horizontalVelocity = new THREE.Vector2(bodyVelocity.x, bodyVelocity.z).length();

    // 如果速度太小，不创建水体分裂效果
    if (horizontalVelocity < 2.0) return;

    // 计算水体分裂效果强度
    const splittingStrength = Math.min(1.0, horizontalVelocity / 10.0) * this.config.splittingEffectStrength!;

    // 计算水体分裂效果位置
    const splittingPosition = new THREE.Vector3(
      bodyPosition.x,
      waterHeight,
      bodyPosition.z
    );

    // 计算水体分裂效果方向
    const splittingDirection = new THREE.Vector2(bodyVelocity.x, bodyVelocity.z).normalize();

    // 发射水体分裂效果事件
    this.eventEmitter.emit(WaterInteractionSystemEventType.WATER_SPLITTING, {
      entityId,
      waterEntityId,
      position: splittingPosition,
      velocity: bodyVelocity.clone(),
      direction: splittingDirection,
      size: bodySize.clone(),
      strength: splittingStrength,
      waterBody
    });

    // 如果水体物理系统存在，通知水体物理系统创建水体分裂效果
    if (this.waterPhysicsSystem) {
      this.waterPhysicsSystem.createSplittingEffect(
        waterEntityId,
        splittingPosition,
        splittingStrength,
        new THREE.Vector3(splittingDirection.x, 0, splittingDirection.y)
      );
    }
  }

  /**
   * 处理物体进入水体
   * @param data 事件数据
   */
  private handleObjectEnterWater(data: any): void {
    // 发射物体进入水体事件
    this.eventEmitter.emit(WaterInteractionSystemEventType.OBJECT_ENTER_WATER, data);

    // 检查是否需要创建水花效果
    if (this.config.enableSplashEffect && data.velocity.y < -0.5) {
      // 计算水花效果强度
      const splashStrength = Math.min(1.0, Math.abs(data.velocity.y) / 10.0) * this.config.splashEffectStrength!;

      // 如果水体物理系统存在，通知水体物理系统创建水花效果
      if (this.waterPhysicsSystem) {
        this.waterPhysicsSystem.createSplashEffect(
          data.waterEntityId,
          data.position,
          splashStrength,
          data.size
        );
      }
    }
  }

  /**
   * 处理物体离开水体
   * @param data 事件数据
   */
  private handleObjectExitWater(data: any): void {
    // 发射物体离开水体事件
    this.eventEmitter.emit(WaterInteractionSystemEventType.OBJECT_EXIT_WATER, data);

    // 检查是否需要创建水滴效果
    if (this.config.enableDropletEffect && data.velocity.y > 0.5) {
      // 计算水滴效果强度
      const dropletStrength = Math.min(1.0, data.velocity.y / 5.0) * this.config.dropletEffectStrength!;

      // 如果水体物理系统存在，通知水体物理系统创建水滴效果
      if (this.waterPhysicsSystem) {
        this.waterPhysicsSystem.createDropletEffect(
          data.waterEntityId,
          data.position,
          dropletStrength,
          data.velocity
        );
      }
    }
  }

  /**
   * 处理物体离开水体（如果需要）
   * @param entityId 物体实体ID
   * @param waterEntityId 水体实体ID
   */
  private handleObjectExitWaterIfNeeded(entityId: number, waterEntityId: number): void {
    // 获取物体水体交互状态
    const objectWaterStates = this.objectWaterInteractionStates.get(entityId);
    if (!objectWaterStates) return;

    const state = objectWaterStates.get(waterEntityId);
    if (!state || !state.isInWater) return;

    // 更新状态
    state.isInWater = false;

    // 如果水体物理系统存在，获取物体和水体
    if (this.physicsSystem && this.waterPhysicsSystem) {
      const physicsBody = this.physicsSystem.getBody(entityId);
      const waterBody = this.waterPhysicsSystem.getWaterBody(waterEntityId);

      if (physicsBody && waterBody) {
        // 处理物体离开水体
        this.handleObjectExitWater({
          entityId,
          waterEntityId,
          position: physicsBody.getPosition(),
          velocity: physicsBody.getVelocity(),
          size: physicsBody.getSize(),
          mass: physicsBody.getMass(),
          waterBody,
          waterHeight: state.waterSurfaceHeight
        });
      }
    }
  }

  /**
   * 处理水体碰撞
   * @param data 事件数据
   */
  private handleWaterCollision(data: any): void {
    // 检查是否需要创建水花效果
    if (this.config.enableSplashEffect) {
      // 计算水花效果强度
      const splashStrength = Math.min(1.0, data.impactVelocity / 10.0) * this.config.splashEffectStrength!;

      // 如果水体物理系统存在，通知水体物理系统创建水花效果
      if (this.waterPhysicsSystem) {
        this.waterPhysicsSystem.createSplashEffect(
          data.waterEntityId,
          data.position,
          splashStrength,
          data.size
        );
      }
    }
  }

  /**
   * 处理水流冲击
   * @param data 事件数据
   */
  private handleWaterFlowImpact(data: any): void {
    // 检查是否需要创建水波纹效果
    if (this.config.enableRippleEffect) {
      // 计算水波纹效果强度
      const rippleStrength = Math.min(1.0, data.impactVelocity / 5.0) * this.config.rippleEffectStrength!;

      // 如果水体物理系统存在，通知水体物理系统创建水波纹效果
      if (this.waterPhysicsSystem) {
        this.waterPhysicsSystem.createRippleEffect(
          data.waterEntityId,
          data.position,
          rippleStrength,
          data.flowDirection
        );
      }
    }
  }

  /**
   * 处理水体分裂
   * @param data 事件数据
   */
  private handleWaterSplitting(data: any): void {
    // 检查是否需要创建水体分裂效果
    if (this.config.enableSplittingEffect) {
      // 计算水体分裂效果强度
      const splittingStrength = Math.min(1.0, data.impactVelocity / 10.0) * this.config.splittingEffectStrength!;

      // 如果水体物理系统存在，通知水体物理系统创建水体分裂效果
      if (this.waterPhysicsSystem) {
        this.waterPhysicsSystem.createSplittingEffect(
          data.waterEntityId,
          data.position,
          splittingStrength,
          data.direction
        );
      }
    }
  }

  /**
   * 更新调试渲染器
   */
  private updateDebugRenderer(): void {
    if (!this.debugRenderer) return;

    // 清除旧的调试渲染
    while (this.debugRenderer.children.length > 0) {
      this.debugRenderer.remove(this.debugRenderer.children[0]);
    }

    // 遍历所有物体水体交互状态
    for (const [entityId, objectWaterStates] of this.objectWaterInteractionStates) {
      for (const [waterEntityId, state] of objectWaterStates) {
        if (!state.isInWater) continue;

        // 获取物体和水体
        const physicsBody = this.physicsSystem?.getBody(entityId);
        const waterBody = this.waterPhysicsSystem?.getWaterBody(waterEntityId);

        if (!physicsBody || !waterBody) continue;

        // 获取物体位置和尺寸
        const bodyPosition = physicsBody.getPosition();
        const bodySize = physicsBody.getSize();

        // 创建调试几何体
        const geometry = new THREE.BoxGeometry(bodySize.x, bodySize.y, bodySize.z);
        const material = new THREE.MeshBasicMaterial({
          color: 0x00ff00,
          wireframe: true,
          transparent: true,
          opacity: 0.3
        });
        const mesh = new THREE.Mesh(geometry, material);
        mesh.position.copy(bodyPosition);

        // 添加到调试渲染器
        this.debugRenderer.add(mesh);

        // 创建浮力箭头
        const waterHeight = state.waterSurfaceHeight;
        const submergedRatio = state.submergedRatio;

        if (submergedRatio > 0) {
          const arrowLength = submergedRatio * 2;
          const arrowHelper = new THREE.ArrowHelper(
            new THREE.Vector3(0, 1, 0),
            new THREE.Vector3(bodyPosition.x, waterHeight, bodyPosition.z),
            arrowLength,
            0x0000ff,
            0.2,
            0.1
          );

          // 添加到调试渲染器
          this.debugRenderer.add(arrowHelper);
        }
      }
    }
  }

  /**
   * 注册事件监听器
   * @param eventType 事件类型
   * @param callback 回调函数
   */
  public on(eventType: WaterInteractionSystemEventType, callback: (data: any) => void): void {
    this.eventEmitter.on(eventType, callback);
  }

  /**
   * 移除事件监听器
   * @param eventType 事件类型
   * @param callback 回调函数
   */
  public off(eventType: WaterInteractionSystemEventType, callback: (data: any) => void): void {
    this.eventEmitter.off(eventType, callback);
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    // 清除调试渲染器
    if (this.debugRenderer) {
      if (this.world) {
        const scene = this.world.getScene();
        if (scene) {
          scene.remove(this.debugRenderer);
        }
      }
      this.debugRenderer = null;
    }

    // 清除事件监听器
    this.eventEmitter.clear();

    // 清除物体水体交互状态
    this.objectWaterInteractionStates.clear();

    // 清除性能监视器
    (this.performanceMonitor as any).dispose();
  }
}
