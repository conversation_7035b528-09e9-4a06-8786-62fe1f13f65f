/**
 * 水体LOD系统
 * 根据距离使用不同细节级别的水体模型
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import { type Camera  } from '../Camera';
import { WaterBodyComponent } from '../../physics/water/WaterBodyComponent';
import { Debug } from '../../utils/Debug';
import { PerformanceMonitor } from '../../utils/PerformanceMonitor';
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 水体LOD级别
 */
export enum WaterLODLevel {
  /** 高质量 */
  HIGH = 0,
  /** 中高质量 */
  MEDIUM_HIGH = 1,
  /** 中质量 */
  MEDIUM = 2,
  /** 中低质量 */
  MEDIUM_LOW = 3,
  /** 低质量 */
  LOW = 4
}

/**
 * 水体LOD配置
 */
export interface WaterLODConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否自动更新 */
  autoUpdate?: boolean;
  /** 更新频率 */
  updateFrequency?: number;
  /** LOD距离 */
  lodDistances?: number[];
  /** 是否使用平滑过渡 */
  useSmoothTransition?: boolean;
  /** 过渡时间（秒） */
  transitionTime?: number;
  /** 是否使用调试可视化 */
  useDebugVisualization?: boolean;
}

/**
 * 水体LOD系统事件类型
 */
export enum WaterLODSystemEventType {
  /** LOD级别变化 */
  LOD_LEVEL_CHANGED = 'lodLevelChanged'
}

/**
 * 水体LOD系统
 */
export class WaterLODSystem extends System {
  /** 系统类型 */
  public static readonly TYPE = 'WaterLODSystem';

  /** 配置 */
  private config: Required<WaterLODConfig>;
  /** 水体实体映射 */
  private waterEntities: Map<Entity, WaterBodyComponent> = new Map();
  /** 实体LOD级别映射 */
  private entityLODLevels: Map<string, WaterLODLevel> = new Map();
  /** 实体过渡状态映射 */
  private entityTransitions: Map<string, {
    fromLevel: WaterLODLevel;
    toLevel: WaterLODLevel;
    progress: number;
    startTime: number;
  }> = new Map();
  /** 帧计数器 */
  private frameCount: number = 0;
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** 性能监视器 */
  private performanceMonitor: PerformanceMonitor = new PerformanceMonitor();
  /** 调试对象 */
  private debugObjects: THREE.Object3D[] = [];

  /**
   * 构造函数
   * @param world 世界
   * @param config 配置
   */
  constructor(world: World, config: WaterLODConfig = {}) {
    super(world);

    // 设置默认配置
    this.config = {
      enabled: config.enabled !== undefined ? config.enabled : true,
      autoUpdate: config.autoUpdate !== undefined ? config.autoUpdate : true,
      updateFrequency: config.updateFrequency || 1,
      lodDistances: config.lodDistances || [100, 300, 600, 1000],
      useSmoothTransition: config.useSmoothTransition !== undefined ? config.useSmoothTransition : true,
      transitionTime: config.transitionTime || 0.5,
      useDebugVisualization: config.useDebugVisualization !== undefined ? config.useDebugVisualization : false
    };

    // 初始化调试可视化
    if (this.config.useDebugVisualization) {
      this.initializeDebugVisualization();
    }
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    super.initialize();
    Debug.log('WaterLODSystem', '水体LOD系统初始化');
  }

  /**
   * 初始化调试可视化
   */
  private initializeDebugVisualization(): void {
    // 创建调试容器
    const debugContainer = new THREE.Object3D();
    debugContainer.name = 'WaterLODDebug';
    this.world.getScene()?.add(debugContainer);
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.enabled || !this.config.autoUpdate) {
      return;
    }

    // 按照更新频率更新
    this.frameCount++;
    if (this.frameCount % this.config.updateFrequency !== 0) {
      return;
    }

    // 如果启用了性能监控，开始计时
    if (this.config.useDebugVisualization) {
      this.performanceMonitor.start('waterLODUpdate');
    }

    // 获取相机
    const camera = this.getCamera();
    if (!camera) {
      return;
    }

    // 更新所有水体的LOD级别
    this.updateWaterLODLevels(camera, deltaTime);

    // 更新调试可视化
    if (this.config.useDebugVisualization) {
      this.updateDebugVisualization();
      this.performanceMonitor.end('waterLODUpdate');
    }
  }

  /**
   * 获取相机
   * @returns 相机
   */
  private getCamera(): Camera | null {
    // 获取相机系统
    const cameraSystem = this.world.getSystem('CameraSystem');
    if (!cameraSystem) {
      return null;
    }

    // 获取活动相机
    return (cameraSystem as any).getActiveCamera();
  }

  /**
   * 添加水体实体
   * @param entity 实体
   * @param component 水体组件
   */
  public addWaterEntity(entity: Entity, component: WaterBodyComponent): void {
    this.waterEntities.set(entity, component);
    this.entityLODLevels.set(entity.id, WaterLODLevel.HIGH);
  }

  /**
   * 移除水体实体
   * @param entity 实体
   */
  public removeWaterEntity(entity: Entity): void {
    this.waterEntities.delete(entity);
    this.entityLODLevels.delete(entity.id);
    this.entityTransitions.delete(entity.id);
  }

  /**
   * 更新水体LOD级别
   * @param camera 相机
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateWaterLODLevels(camera: Camera, deltaTime: number): void {
    // 获取相机位置
    const cameraPosition = camera.getThreeCamera().position;

    // 遍历所有水体实体
    for (const [entity, component] of this.waterEntities.entries()) {
      // 获取水体位置
      const waterPosition = component.getPosition();

      // 计算相机到水体的距离
      const distance = cameraPosition.distanceTo(waterPosition);

      // 确定LOD级别
      const newLevel = this.determineLODLevel(distance);

      // 获取当前LOD级别
      const currentLevel = this.entityLODLevels.get(entity.id) || WaterLODLevel.HIGH;

      // 如果LOD级别发生变化
      if (newLevel !== currentLevel) {
        // 如果使用平滑过渡
        if (this.config.useSmoothTransition) {
          // 开始过渡
          this.startTransition(entity.id, currentLevel, newLevel);
        } else {
          // 直接设置新的LOD级别
          this.setEntityLODLevel(entity.id, newLevel);
        }
      }
    }

    // 更新过渡状态
    if (this.config.useSmoothTransition) {
      this.updateTransitions(deltaTime);
    }
  }

  /**
   * 确定LOD级别
   * @param distance 距离
   * @returns LOD级别
   */
  private determineLODLevel(distance: number): WaterLODLevel {
    // 根据距离确定LOD级别
    for (let i = 0; i < this.config.lodDistances.length; i++) {
      if (distance < this.config.lodDistances[i]) {
        return i as WaterLODLevel;
      }
    }
    return WaterLODLevel.LOW;
  }

  /**
   * 设置实体LOD级别
   * @param entityId 实体ID
   * @param level LOD级别
   */
  private setEntityLODLevel(entityId: string, level: WaterLODLevel): void {
    // 获取旧的LOD级别
    const oldLevel = this.entityLODLevels.get(entityId);

    // 设置新的LOD级别
    this.entityLODLevels.set(entityId, level);

    // 发出事件
    this.eventEmitter.emit(WaterLODSystemEventType.LOD_LEVEL_CHANGED, entityId, oldLevel, level);

    // 获取实体
    const entity = this.world.getEntityById(entityId);
    if (!entity) {
      return;
    }

    // 获取水体组件
    const component = this.waterEntities.get(entity);
    if (!component) {
      return;
    }

    // 更新水体材质
    this.updateWaterMaterial(component, level);

    Debug.log('WaterLODSystem', `实体 ${entityId} 的LOD级别从 ${oldLevel} 变为 ${level}`);
  }

  /**
   * 开始过渡
   * @param entityId 实体ID
   * @param fromLevel 起始LOD级别
   * @param toLevel 目标LOD级别
   */
  private startTransition(entityId: string, fromLevel: WaterLODLevel, toLevel: WaterLODLevel): void {
    // 设置过渡状态
    this.entityTransitions.set(entityId, {
      fromLevel,
      toLevel,
      progress: 0,
      startTime: performance.now()
    });

    Debug.log('WaterLODSystem', `实体 ${entityId} 开始从LOD级别 ${fromLevel} 过渡到 ${toLevel}`);
  }

  /**
   * 更新过渡状态
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateTransitions(deltaTime: number): void {
    // 获取当前时间
    const currentTime = performance.now();

    // 遍历所有过渡状态
    for (const [entityId, transition] of this.entityTransitions.entries()) {
      // 计算过渡进度
      const elapsedTime = (currentTime - transition.startTime) / 1000; // 转换为秒
      const progress = Math.min(1, elapsedTime / this.config.transitionTime);

      // 更新过渡进度
      transition.progress = progress;

      // 获取实体
      const entity = this.world.getEntityById(entityId);
      if (!entity) {
        continue;
      }

      // 获取水体组件
      const component = this.waterEntities.get(entity);
      if (!component) {
        continue;
      }

      // 更新水体材质
      this.updateWaterMaterialTransition(component, transition.fromLevel, transition.toLevel, progress);

      // 如果过渡完成
      if (progress >= 1) {
        // 设置新的LOD级别
        this.setEntityLODLevel(entityId, transition.toLevel);
        // 移除过渡状态
        this.entityTransitions.delete(entityId);
      }
    }
  }

  /**
   * 更新水体材质
   * @param component 水体组件
   * @param level LOD级别
   */
  private updateWaterMaterial(component: WaterBodyComponent, level: WaterLODLevel): void {
    // 获取水体材质
    const material = component.getMaterial();
    if (!material) {
      return;
    }

    // 根据LOD级别调整材质参数
    if (material instanceof THREE.ShaderMaterial) {
      const uniforms = material.uniforms;

      // 调整波浪强度
      if (uniforms.waveStrength) {
        uniforms.waveStrength.value = this.getWaveStrengthForLevel(level);
      }

      // 调整反射强度
      if (uniforms.reflectionStrength) {
        uniforms.reflectionStrength.value = this.getReflectionStrengthForLevel(level);
      }

      // 调整折射强度
      if (uniforms.refractionStrength) {
        uniforms.refractionStrength.value = this.getRefractionStrengthForLevel(level);
      }

      // 调整法线贴图缩放
      if (uniforms.normalScale) {
        uniforms.normalScale.value = this.getNormalScaleForLevel(level);
      }

      // 调整水深颜色混合
      if (uniforms.waterDepthFactor) {
        uniforms.waterDepthFactor.value = this.getWaterDepthFactorForLevel(level);
      }
    }

    // 调整几何体细节
    this.updateWaterGeometry(component, level);
  }

  /**
   * 更新水体材质过渡
   * @param component 水体组件
   * @param fromLevel 起始LOD级别
   * @param toLevel 目标LOD级别
   * @param progress 过渡进度
   */
  private updateWaterMaterialTransition(
    component: WaterBodyComponent,
    fromLevel: WaterLODLevel,
    toLevel: WaterLODLevel,
    progress: number
  ): void {
    // 获取水体材质
    const material = component.getMaterial();
    if (!material) {
      return;
    }

    // 根据过渡进度插值材质参数
    if (material instanceof THREE.ShaderMaterial) {
      const uniforms = material.uniforms;

      // 插值波浪强度
      if (uniforms.waveStrength) {
        const fromStrength = this.getWaveStrengthForLevel(fromLevel);
        const toStrength = this.getWaveStrengthForLevel(toLevel);
        uniforms.waveStrength.value = THREE.MathUtils.lerp(fromStrength, toStrength, progress);
      }

      // 插值反射强度
      if (uniforms.reflectionStrength) {
        const fromStrength = this.getReflectionStrengthForLevel(fromLevel);
        const toStrength = this.getReflectionStrengthForLevel(toLevel);
        uniforms.reflectionStrength.value = THREE.MathUtils.lerp(fromStrength, toStrength, progress);
      }

      // 插值折射强度
      if (uniforms.refractionStrength) {
        const fromStrength = this.getRefractionStrengthForLevel(fromLevel);
        const toStrength = this.getRefractionStrengthForLevel(toLevel);
        uniforms.refractionStrength.value = THREE.MathUtils.lerp(fromStrength, toStrength, progress);
      }

      // 插值法线贴图缩放
      if (uniforms.normalScale) {
        const fromScale = this.getNormalScaleForLevel(fromLevel);
        const toScale = this.getNormalScaleForLevel(toLevel);
        uniforms.normalScale.value = THREE.MathUtils.lerp(fromScale, toScale, progress);
      }

      // 插值水深颜色混合
      if (uniforms.waterDepthFactor) {
        const fromFactor = this.getWaterDepthFactorForLevel(fromLevel);
        const toFactor = this.getWaterDepthFactorForLevel(toLevel);
        uniforms.waterDepthFactor.value = THREE.MathUtils.lerp(fromFactor, toFactor, progress);
      }
    }
  }

  /**
   * 更新水体几何体
   * @param component 水体组件
   * @param level LOD级别
   */
  private updateWaterGeometry(component: WaterBodyComponent, level: WaterLODLevel): void {
    // 获取水体网格
    const mesh = component.getMesh();
    if (!mesh) {
      return;
    }

    // 获取当前几何体
    const currentGeometry = mesh.geometry;
    if (!(currentGeometry instanceof THREE.PlaneGeometry)) {
      return;
    }

    // 获取当前分辨率
    const currentResolution = this.getGeometryResolution(currentGeometry);

    // 获取目标分辨率
    const targetResolution = this.getResolutionForLevel(level);

    // 如果分辨率相同，则跳过
    if (currentResolution === targetResolution) {
      return;
    }

    // 创建新几何体
    const newGeometry = new THREE.PlaneGeometry(
      currentGeometry.parameters.width,
      currentGeometry.parameters.height,
      targetResolution,
      targetResolution
    );

    // 旋转几何体，使其水平
    newGeometry.rotateX(-Math.PI / 2);

    // 替换几何体
    (mesh.geometry as any).dispose();
    mesh.geometry = newGeometry;
  }

  /**
   * 获取几何体分辨率
   * @param geometry 几何体
   * @returns 分辨率
   */
  private getGeometryResolution(geometry: THREE.PlaneGeometry): number {
    return geometry.parameters.widthSegments;
  }

  /**
   * 根据LOD级别获取分辨率
   * @param level LOD级别
   * @returns 分辨率
   */
  private getResolutionForLevel(level: WaterLODLevel): number {
    // 根据LOD级别返回不同的分辨率
    switch (level) {
      case WaterLODLevel.HIGH: return 64; // 最高质量
      case WaterLODLevel.MEDIUM_HIGH: return 32;
      case WaterLODLevel.MEDIUM: return 16;
      case WaterLODLevel.MEDIUM_LOW: return 8;
      case WaterLODLevel.LOW: return 4; // 最低质量
      default: return 16;
    }
  }

  /**
   * 根据LOD级别获取波浪强度
   * @param level LOD级别
   * @returns 波浪强度
   */
  private getWaveStrengthForLevel(level: WaterLODLevel): number {
    // 根据LOD级别返回不同的波浪强度
    switch (level) {
      case WaterLODLevel.HIGH: return 1.0;
      case WaterLODLevel.MEDIUM_HIGH: return 0.8;
      case WaterLODLevel.MEDIUM: return 0.6;
      case WaterLODLevel.MEDIUM_LOW: return 0.4;
      case WaterLODLevel.LOW: return 0.2;
      default: return 0.6;
    }
  }

  /**
   * 根据LOD级别获取反射强度
   * @param level LOD级别
   * @returns 反射强度
   */
  private getReflectionStrengthForLevel(level: WaterLODLevel): number {
    // 根据LOD级别返回不同的反射强度
    switch (level) {
      case WaterLODLevel.HIGH: return 1.0;
      case WaterLODLevel.MEDIUM_HIGH: return 0.8;
      case WaterLODLevel.MEDIUM: return 0.6;
      case WaterLODLevel.MEDIUM_LOW: return 0.4;
      case WaterLODLevel.LOW: return 0.2;
      default: return 0.6;
    }
  }

  /**
   * 根据LOD级别获取折射强度
   * @param level LOD级别
   * @returns 折射强度
   */
  private getRefractionStrengthForLevel(level: WaterLODLevel): number {
    // 根据LOD级别返回不同的折射强度
    switch (level) {
      case WaterLODLevel.HIGH: return 1.0;
      case WaterLODLevel.MEDIUM_HIGH: return 0.8;
      case WaterLODLevel.MEDIUM: return 0.6;
      case WaterLODLevel.MEDIUM_LOW: return 0.4;
      case WaterLODLevel.LOW: return 0.2;
      default: return 0.6;
    }
  }

  /**
   * 根据LOD级别获取法线贴图缩放
   * @param level LOD级别
   * @returns 法线贴图缩放
   */
  private getNormalScaleForLevel(level: WaterLODLevel): number {
    // 根据LOD级别返回不同的法线贴图缩放
    switch (level) {
      case WaterLODLevel.HIGH: return 1.0;
      case WaterLODLevel.MEDIUM_HIGH: return 0.8;
      case WaterLODLevel.MEDIUM: return 0.6;
      case WaterLODLevel.MEDIUM_LOW: return 0.4;
      case WaterLODLevel.LOW: return 0.2;
      default: return 0.6;
    }
  }

  /**
   * 根据LOD级别获取水深颜色混合因子
   * @param level LOD级别
   * @returns 水深颜色混合因子
   */
  private getWaterDepthFactorForLevel(level: WaterLODLevel): number {
    // 根据LOD级别返回不同的水深颜色混合因子
    switch (level) {
      case WaterLODLevel.HIGH: return 1.0;
      case WaterLODLevel.MEDIUM_HIGH: return 0.8;
      case WaterLODLevel.MEDIUM: return 0.6;
      case WaterLODLevel.MEDIUM_LOW: return 0.4;
      case WaterLODLevel.LOW: return 0.2;
      default: return 0.6;
    }
  }

  /**
   * 更新调试可视化
   */
  private updateDebugVisualization(): void {
    // 清除旧的调试对象
    for (const obj of this.debugObjects) {
      this.world.getScene()?.remove(obj);
    }
    this.debugObjects = [];

    // 创建新的调试对象
    for (const [entity, component] of this.waterEntities.entries()) {
      // 获取LOD级别
      const level = this.entityLODLevels.get(entity.id) || WaterLODLevel.HIGH;

      // 获取水体位置
      const position = component.getPosition();

      // 创建文本标签
      // 这里需要使用TextSprite或类似的库来创建3D文本
      // 暂时省略

      // 创建颜色指示器
      const color = this.getLODLevelColor(level);
      const indicator = new THREE.Mesh(
        new THREE.SphereGeometry(1, 8, 8),
        new THREE.MeshBasicMaterial({ color })
      );
      indicator.position.copy(position);
      (indicator as any).getPosition().y += 5;
      this.world.getScene()?.add(indicator);
      this.debugObjects.push(indicator);
    }
  }

  /**
   * 获取LOD级别颜色
   * @param level LOD级别
   * @returns 颜色
   */
  private getLODLevelColor(level: WaterLODLevel): THREE.Color {
    // 根据LOD级别返回不同的颜色
    switch (level) {
      case WaterLODLevel.HIGH: return new THREE.Color(0, 1, 0); // 绿色
      case WaterLODLevel.MEDIUM_HIGH: return new THREE.Color(0.5, 1, 0); // 黄绿色
      case WaterLODLevel.MEDIUM: return new THREE.Color(1, 1, 0); // 黄色
      case WaterLODLevel.MEDIUM_LOW: return new THREE.Color(1, 0.5, 0); // 橙色
      case WaterLODLevel.LOW: return new THREE.Color(1, 0, 0); // 红色
      default: return new THREE.Color(1, 1, 1); // 白色
    }
  }
}
