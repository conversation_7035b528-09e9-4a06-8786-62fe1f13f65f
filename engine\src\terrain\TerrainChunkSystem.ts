/**
 * 地形分块系统
 * 负责地形的分块管理和渲染优化
 */
import * as THREE from 'three';
import { System } from '../core/System';
import type { Entity } from '../core/Entity';
import { type Camera  } from '../rendering/Camera';
import { Scene } from '../scene/Scene';
import { TerrainComponent } from './components/TerrainComponent';
import { TerrainMaterial } from './materials/TerrainMaterial';
import { TerrainUtils } from './utils/TerrainUtils';
import { Debug } from '../utils/Debug';
import { EventEmitter } from '../utils/EventEmitter';
import { Octree } from '../utils/Octree';

/**
 * 地形块接口
 */
export interface TerrainChunk {
  /** 块ID */
  id: string;
  /** 块索引X */
  indexX: number;
  /** 块索引Z */
  indexZ: number;
  /** 块大小 */
  size: number;
  /** 块分辨率 */
  resolution: number;
  /** 块中心位置 */
  center: THREE.Vector3;
  /** 块包围盒 */
  boundingBox: THREE.Box3;
  /** 块包围球 */
  boundingSphere: THREE.Sphere;
  /** 块LOD级别 */
  lodLevel: number;
  /** 块网格 */
  mesh: THREE.Mesh | null;
  /** 块几何体 */
  geometry: THREE.BufferGeometry | null;
  /** 块材质 */
  material: THREE.Material | null;
  /** 块高度数据 */
  heightData: Float32Array;
  /** 块法线数据 */
  normalData: Float32Array;
  /** 是否可见 */
  visible: boolean;
  /** 是否已加载 */
  loaded: boolean;
  /** 是否需要更新 */
  needsUpdate: boolean;
}

/**
 * 地形分块系统事件类型
 */
export enum TerrainChunkSystemEventType {
  /** 块创建 */
  CHUNK_CREATED = 'chunk_created',
  /** 块销毁 */
  CHUNK_DESTROYED = 'chunk_destroyed',
  /** 块可见性变更 */
  CHUNK_VISIBILITY_CHANGED = 'chunk_visibility_changed',
  /** 块LOD级别变更 */
  CHUNK_LOD_CHANGED = 'chunk_lod_changed',
  /** 块更新 */
  CHUNK_UPDATED = 'chunk_updated'
}

/**
 * 地形分块系统配置接口
 */
export interface TerrainChunkSystemOptions {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否自动更新 */
  autoUpdate?: boolean;
  /** 更新频率 */
  updateFrequency?: number;
  /** 块大小 */
  chunkSize?: number;
  /** 块分辨率 */
  chunkResolution?: number;
  /** 是否使用八叉树 */
  useOctree?: boolean;
  /** 是否使用视锥体剔除 */
  useFrustumCulling?: boolean;
  /** 是否使用LOD */
  useLOD?: boolean;
  /** LOD距离 */
  lodDistances?: number[];
  /** 是否使用GPU实例化 */
  useGPUInstancing?: boolean;
  /** 是否使用调试可视化 */
  useDebugVisualization?: boolean;
}

/**
 * 地形分块系统类
 */
export class TerrainChunkSystem extends System {
  /** 系统类型 */
  public static readonly TYPE: string = 'TerrainChunkSystem';

  /** 是否启用 */
  private enabled: boolean;

  /** 是否自动更新 */
  private autoUpdate: boolean;

  /** 更新频率 */
  private updateFrequency: number;

  /** 块大小 */
  private chunkSize: number;

  /** 块分辨率 */
  private chunkResolution: number;

  /** 是否使用八叉树 */
  private useOctree: boolean;

  /** 是否使用视锥体剔除 */
  private useFrustumCulling: boolean;

  /** 是否使用LOD */
  private useLOD: boolean;

  /** LOD距离 */
  private lodDistances: number[];

  /** 是否使用GPU实例化 */
  private useGPUInstancing: boolean;

  /** 是否使用调试可视化 */
  private useDebugVisualization: boolean;

  /** 地形实体映射 */
  private terrainEntities: Map<Entity, TerrainComponent>;

  /** 地形块映射 */
  private terrainChunks: Map<string, TerrainChunk>;

  /** 实体到块映射 */
  private entityToChunks: Map<string, Set<string>>;

  /** 八叉树 */
  private octree: Octree | null;

  /** 视锥体 */
  private frustum: THREE.Frustum;

  /** 帧计数器 */
  private frameCount: number;

  /** 事件发射器 */
  private eventEmitter: EventEmitter;

  /** 调试网格 */
  private debugMeshes: THREE.Mesh[];

  /** 块计数器 */
  private chunkCounter: number;

  /**
   * 创建地形分块系统
   * @param options 配置选项
   */
  constructor(options: TerrainChunkSystemOptions = {}) {
    super();

    this.enabled = options.enabled !== undefined ? options.enabled : true;
    this.autoUpdate = options.autoUpdate !== undefined ? options.autoUpdate : true;
    this.updateFrequency = options.updateFrequency || 1;
    this.chunkSize = options.chunkSize || 100;
    this.chunkResolution = options.chunkResolution || 32;
    this.useOctree = options.useOctree !== undefined ? options.useOctree : true;
    this.useFrustumCulling = options.useFrustumCulling !== undefined ? options.useFrustumCulling : true;
    this.useLOD = options.useLOD !== undefined ? options.useLOD : true;
    this.lodDistances = options.lodDistances || [100, 300, 600, 1200];
    this.useGPUInstancing = options.useGPUInstancing !== undefined ? options.useGPUInstancing : true;
    this.useDebugVisualization = options.useDebugVisualization !== undefined ? options.useDebugVisualization : false;

    this.terrainEntities = new Map();
    this.terrainChunks = new Map();
    this.entityToChunks = new Map();
    this.octree = null;
    this.frustum = new THREE.Frustum();
    this.frameCount = 0;
    this.eventEmitter = new EventEmitter();
    this.debugMeshes = [];
    this.chunkCounter = 0;
  }

  /**
   * 获取系统类型
   * @returns 系统类型
   */
  public getType(): string {
    return TerrainChunkSystem.TYPE;
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    // 初始化八叉树
    if (this.useOctree) {
      this.initializeOctree();
    }
  }

  /**
   * 初始化八叉树
   */
  private initializeOctree(): void {
    // 创建八叉树
    this.octree = new Octree({
      size: 10000, // 足够大以包含所有地形
      maxDepth: 8,
      maxObjects: 100
    });
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.enabled || !this.autoUpdate) {
      return;
    }

    // 按照更新频率更新
    this.frameCount++;
    if (this.frameCount % this.updateFrequency !== 0) {
      return;
    }

    // 获取相机
    const camera = this.getCamera();
    if (!camera) {
      return;
    }

    // 更新视锥体
    if (this.useFrustumCulling) {
      this.updateFrustum(camera);
    }

    // 更新所有块
    this.updateChunks(camera);

    // 更新调试可视化
    if (this.useDebugVisualization) {
      this.updateDebugVisualization();
    }
  }

  /**
   * 添加地形实体
   * @param entity 实体
   * @param component 地形组件
   */
  public addTerrainEntity(entity: Entity, component: TerrainComponent): void {
    this.terrainEntities.set(entity, component);
    this.entityToChunks.set(entity.id, new Set());
    this.createChunksForTerrain(entity, component);
  }

  /**
   * 移除地形实体
   * @param entity 实体
   */
  public removeTerrainEntity(entity: Entity): void {
    // 获取实体关联的块
    const chunkIds = this.entityToChunks.get(entity.id);
    if (chunkIds) {
      // 销毁所有块
      for (const chunkId of chunkIds) {
        this.destroyChunk(chunkId);
      }
      this.entityToChunks.delete(entity.id);
    }

    // 从地形实体映射中移除
    this.terrainEntities.delete(entity);
  }

  /**
   * 为地形创建块
   * @param entity 实体
   * @param component 地形组件
   */
  private createChunksForTerrain(entity: Entity, component: TerrainComponent): void {
    // 计算需要的块数量
    const chunksX = Math.ceil(component.width / this.chunkSize);
    const chunksZ = Math.ceil(component.height / this.chunkSize);

    // 创建所有块
    for (let z = 0; z < chunksZ; z++) {
      for (let x = 0; x < chunksX; x++) {
        this.createTerrainChunk(entity, component, x, z);
      }
    }
  }

  /**
   * 创建地形块
   * @param entity 实体
   * @param component 地形组件
   * @param indexX X索引
   * @param indexZ Z索引
   * @returns 块ID
   */
  private createTerrainChunk(entity: Entity, component: TerrainComponent, indexX: number, indexZ: number): string {
    // 生成块ID
    const chunkId = `chunk_${entity.id}_${indexX}_${indexZ}_${this.chunkCounter++}`;

    // 计算块位置和大小
    const offsetX = indexX * this.chunkSize - component.width / 2;
    const offsetZ = indexZ * this.chunkSize - component.height / 2;
    const sizeX = Math.min(this.chunkSize, component.width - indexX * this.chunkSize);
    const sizeZ = Math.min(this.chunkSize, component.height - indexZ * this.chunkSize);

    // 创建包围盒
    const center = new THREE.Vector3(
      offsetX + sizeX / 2,
      0,
      offsetZ + sizeZ / 2
    );
    const boundingBox = new THREE.Box3(
      new THREE.Vector3(offsetX, -component.maxHeight, offsetZ),
      new THREE.Vector3(offsetX + sizeX, component.maxHeight, offsetZ + sizeZ)
    );
    const boundingSphere = new THREE.Sphere(center, Math.max(sizeX, sizeZ, component.maxHeight) / 2);

    // 创建高度数据和法线数据
    const resolution = this.chunkResolution;
    const heightData = new Float32Array(resolution * resolution);
    const normalData = new Float32Array(resolution * resolution * 3);

    // 从地形组件中提取高度数据
    this.extractHeightData(component, indexX, indexZ, heightData);

    // 创建块
    const chunk: TerrainChunk = {
      id: chunkId,
      indexX,
      indexZ,
      size: this.chunkSize,
      resolution,
      center,
      boundingBox,
      boundingSphere,
      lodLevel: 0,
      mesh: null,
      geometry: null,
      material: null,
      heightData,
      normalData,
      visible: false,
      loaded: false,
      needsUpdate: true
    };

    // 添加到块映射
    this.terrainChunks.set(chunkId, chunk);

    // 添加到实体块映射
    const entityChunks = this.entityToChunks.get(entity.id);
    if (entityChunks) {
      entityChunks.add(chunkId);
    }

    // 如果使用八叉树，添加到八叉树
    if (this.useOctree && this.octree) {
      this.octree.insert(chunkId, center, boundingSphere.radius);
    }

    // 初始化块
    this.initializeChunk(entity, component, chunk);

    // 发出块创建事件
    this.eventEmitter.emit(TerrainChunkSystemEventType.CHUNK_CREATED, chunk);

    return chunkId;
  }

  /**
   * 从地形组件中提取高度数据
   * @param component 地形组件
   * @param indexX X索引
   * @param indexZ Z索引
   * @param heightData 高度数据
   */
  private extractHeightData(component: TerrainComponent, indexX: number, indexZ: number, heightData: Float32Array): void {
    const terrainResolution = component.resolution;
    const chunkResolution = this.chunkResolution;

    // 计算块在地形中的起始位置
    const startX = Math.floor((indexX * this.chunkSize / component.width) * (terrainResolution - 1));
    const startZ = Math.floor((indexZ * this.chunkSize / component.height) * (terrainResolution - 1));

    // 计算块在地形中的结束位置
    const endX = Math.ceil(((indexX + 1) * this.chunkSize / component.width) * (terrainResolution - 1));
    const endZ = Math.ceil(((indexZ + 1) * this.chunkSize / component.height) * (terrainResolution - 1));

    // 计算采样步长
    const stepX = (endX - startX) / (chunkResolution - 1);
    const stepZ = (endZ - startZ) / (chunkResolution - 1);

    // 提取高度数据
    for (let z = 0; z < chunkResolution; z++) {
      for (let x = 0; x < chunkResolution; x++) {
        // 计算采样位置
        const sampleX = Math.min(Math.floor(startX + x * stepX), terrainResolution - 1);
        const sampleZ = Math.min(Math.floor(startZ + z * stepZ), terrainResolution - 1);

        // 获取高度值
        const height = component.heightData[sampleZ * terrainResolution + sampleX];

        // 存储高度值
        heightData[z * chunkResolution + x] = height;
      }
    }
  }

  /**
   * 初始化块
   * @param entity 实体
   * @param component 地形组件
   * @param chunk 地形块
   */
  private initializeChunk(entity: Entity, component: TerrainComponent, chunk: TerrainChunk): void {
    // 创建几何体
    chunk.geometry = this.createChunkGeometry(chunk, component.maxHeight);

    // 创建材质
    chunk.material = this.createChunkMaterial(component, chunk);

    // 创建网格
    chunk.mesh = new THREE.Mesh(chunk.geometry, chunk.material);
    chunk.mesh.name = `TerrainChunk_${chunk.id}`;
    chunk.mesh.castShadow = true;
    chunk.mesh.receiveShadow = true;
    chunk.mesh.userData.chunkId = chunk.id;

    // 设置网格位置
    (chunk.mesh as any).setPosition(chunk.center.x, 0, chunk.center.z);

    // 添加到场景
    entity.add(chunk.mesh);

    // 标记为已加载
    chunk.loaded = true;
    chunk.needsUpdate = false;
  }

  /**
   * 创建块几何体
   * @param chunk 地形块
   * @param maxHeight 最大高度
   * @returns 几何体
   */
  private createChunkGeometry(chunk: TerrainChunk, maxHeight: number): THREE.BufferGeometry {
    const resolution = chunk.resolution;
    const size = chunk.size;

    // 创建平面几何体
    const geometry = new THREE.PlaneGeometry(
      size,
      size,
      resolution - 1,
      resolution - 1
    );
    geometry.rotateX(-Math.PI / 2);

    // 更新顶点高度
    const positions = geometry.attributes.position.array as Float32Array;
    for (let i = 0, j = 0; i < positions.length; i += 3, j++) {
      positions[i + 1] = chunk.heightData[j] * maxHeight;
    }

    // 计算法线和切线
    geometry.computeVertexNormals();
    geometry.computeTangents();

    // 存储法线数据
    const normals = geometry.attributes.normal.array as Float32Array;
    for (let i = 0; i < normals.length; i++) {
      chunk.normalData[i] = normals[i];
    }

    return geometry;
  }

  /**
   * 创建块材质
   * @param component 地形组件
   * @param chunk 地形块
   * @returns 材质
   */
  private createChunkMaterial(component: TerrainComponent, chunk: TerrainChunk): THREE.Material {
    // 使用与地形组件相同的材质
    if (component.material) {
      return component.material.clone();
    }

    // 创建默认材质
    return new THREE.MeshStandardMaterial({
      color: 0x888888,
      roughness: 0.8,
      metalness: 0.2,
      wireframe: false
    });
  }

  /**
   * 销毁块
   * @param chunkId 块ID
   */
  private destroyChunk(chunkId: string): void {
    const chunk = this.terrainChunks.get(chunkId);
    if (!chunk) {
      return;
    }

    // 从场景中移除网格
    if (chunk.mesh) {
      if (chunk.mesh.parent) {
        chunk.mesh.parent.remove(chunk.mesh);
      }

      // 释放资源
      if (chunk.geometry) {
        (chunk.geometry as any).dispose();
      }

      if (chunk.material) {
        if (Array.isArray(chunk.material)) {
          chunk.material.forEach(material => (material as any).dispose());
        } else {
          (chunk.material as any).dispose();
        }
      }
    }

    // 从八叉树中移除
    if (this.useOctree && this.octree) {
      this.octree.remove(chunkId);
    }

    // 从块映射中移除
    this.terrainChunks.delete(chunkId);

    // 发出块销毁事件
    this.eventEmitter.emit(TerrainChunkSystemEventType.CHUNK_DESTROYED, chunk);
  }

  /**
   * 更新块
   * @param chunk 地形块
   */
  private updateChunk(chunk: TerrainChunk): void {
    if (!chunk.needsUpdate || !chunk.geometry || !chunk.mesh) {
      return;
    }

    // 更新几何体顶点高度
    const positions = chunk.geometry.attributes.position.array as Float32Array;
    for (let i = 0, j = 0; i < positions.length; i += 3, j++) {
      positions[i + 1] = chunk.heightData[j];
    }

    // 更新几何体
    chunk.geometry.attributes.position.needsUpdate = true;
    chunk.geometry.computeVertexNormals();
    chunk.geometry.computeTangents();

    // 更新法线数据
    const normals = chunk.geometry.attributes.normal.array as Float32Array;
    for (let i = 0; i < normals.length; i++) {
      chunk.normalData[i] = normals[i];
    }

    chunk.needsUpdate = false;

    // 发出块更新事件
    this.eventEmitter.emit(TerrainChunkSystemEventType.CHUNK_UPDATED, chunk);
  }

  /**
   * 获取相机
   * @returns 相机
   */
  private getCamera(): Camera | null {
    // 获取相机组件
    const cameras = this.entityManager.getComponentsOfType<Camera>('Camera');
    if (cameras.length === 0) {
      return null;
    }

    // 返回第一个相机
    return cameras[0];
  }

  /**
   * 更新视锥体
   * @param camera 相机
   */
  private updateFrustum(camera: Camera): void {
    // 获取相机视锥体
    const projScreenMatrix = new THREE.Matrix4();
    projScreenMatrix.multiplyMatrices(
      camera.getThreeCamera().projectionMatrix,
      camera.getThreeCamera().matrixWorldInverse
    );
    this.frustum.setFromProjectionMatrix(projScreenMatrix);
  }

  /**
   * 更新所有块
   * @param camera 相机
   */
  private updateChunks(camera: Camera): void {
    // 获取相机位置
    const cameraPosition = camera.getThreeCamera().position;

    // 如果使用八叉树，使用八叉树更新块
    if (this.useOctree && this.octree) {
      this.updateChunksWithOctree(camera);
    } else {
      // 否则，遍历所有块
      this.updateChunksWithBruteForce(camera);
    }
  }

  /**
   * 使用八叉树更新块
   * @param camera 相机
   */
  private updateChunksWithOctree(camera: Camera): void {
    if (!this.octree) {
      return;
    }

    // 获取相机位置
    const cameraPosition = camera.getThreeCamera().position;

    // 获取视锥体内的节点
    const visibleNodes = this.octree.getFrustumIntersectedNodes(this.frustum);

    // 遍历可见节点
    for (const node of visibleNodes) {
      // 获取节点中的块
      const chunksInNode = node.objects as string[];

      // 遍历块
      for (const chunkId of chunksInNode) {
        const chunk = this.terrainChunks.get(chunkId);
        if (!chunk) {
          continue;
        }

        // 计算相机到块中心的距离
        const distance = cameraPosition.distanceTo(chunk.center);

        // 更新块的LOD级别
        if (this.useLOD) {
          this.updateChunkLOD(chunk, distance);
        }

        // 设置块可见
        this.setChunkVisible(chunk, true);
      }
    }

    // 隐藏不在视锥体内的块
    for (const chunk of this.terrainChunks.values()) {
      if (chunk.visible) {
        // 检查块是否在视锥体内
        if (!this.frustum.intersectsSphere(chunk.boundingSphere)) {
          this.setChunkVisible(chunk, false);
        }
      }
    }
  }

  /**
   * 使用暴力方法更新块
   * @param camera 相机
   */
  private updateChunksWithBruteForce(camera: Camera): void {
    // 获取相机位置
    const cameraPosition = camera.getThreeCamera().position;

    // 遍历所有块
    for (const chunk of this.terrainChunks.values()) {
      // 计算相机到块中心的距离
      const distance = cameraPosition.distanceTo(chunk.center);

      // 检查块是否在视锥体内
      const isVisible = !this.useFrustumCulling || this.frustum.intersectsSphere(chunk.boundingSphere);

      // 设置块可见性
      this.setChunkVisible(chunk, isVisible);

      // 更新块的LOD级别
      if (this.useLOD && isVisible) {
        this.updateChunkLOD(chunk, distance);
      }
    }
  }

  /**
   * 设置块可见性
   * @param chunk 地形块
   * @param visible 是否可见
   */
  private setChunkVisible(chunk: TerrainChunk, visible: boolean): void {
    if (chunk.visible === visible) {
      return;
    }

    chunk.visible = visible;

    if (chunk.mesh) {
      chunk.mesh.visible = visible;
    }

    // 发出块可见性变更事件
    this.eventEmitter.emit(TerrainChunkSystemEventType.CHUNK_VISIBILITY_CHANGED, chunk, visible);
  }

  /**
   * 更新块的LOD级别
   * @param chunk 地形块
   * @param distance 距离
   */
  private updateChunkLOD(chunk: TerrainChunk, distance: number): void {
    // 确定LOD级别
    let lodLevel = 0;
    for (let i = 0; i < this.lodDistances.length; i++) {
      if (distance > this.lodDistances[i]) {
        lodLevel = i + 1;
      }
    }

    // 如果LOD级别没有变化，则返回
    if (chunk.lodLevel === lodLevel) {
      return;
    }

    // 更新LOD级别
    chunk.lodLevel = lodLevel;

    // 发出块LOD级别变更事件
    this.eventEmitter.emit(TerrainChunkSystemEventType.CHUNK_LOD_CHANGED, chunk, lodLevel);
  }

  /**
   * 更新调试可视化
   */
  private updateDebugVisualization(): void {
    // 清除现有的调试网格
    for (const mesh of this.debugMeshes) {
      if (mesh.parent) {
        mesh.parent.remove(mesh);
      }
    }
    this.debugMeshes = [];

    // 获取场景
    const scene = this.getScene();
    if (!scene) {
      return;
    }

    // 创建调试材质
    const visibleMaterial = new THREE.MeshBasicMaterial({
      color: 0x00ff00,
      wireframe: true,
      transparent: true,
      opacity: 0.3
    });
    const hiddenMaterial = new THREE.MeshBasicMaterial({
      color: 0xff0000,
      wireframe: true,
      transparent: true,
      opacity: 0.3
    });

    // 遍历所有块
    for (const chunk of this.terrainChunks.values()) {
      // 创建包围盒网格
      const size = new THREE.Vector3();
      chunk.boundingBox.getSize(size);
      const geometry = new THREE.BoxGeometry(size.x, size.y, size.z);
      const material = chunk.visible ? visibleMaterial : hiddenMaterial;
      const mesh = new THREE.Mesh(geometry, material);

      // 设置位置
      const center = new THREE.Vector3();
      chunk.boundingBox.getCenter(center);
      mesh.position.copy(center);

      // 添加到场景
      scene.getThreeScene().add(mesh);
      this.debugMeshes.push(mesh);
    }
  }

  /**
   * 获取场景
   * @returns 场景
   */
  private getScene(): Scene | null {
    // 获取场景组件
    const scenes = this.entityManager.getComponentsOfType<Scene>('Scene');
    if (scenes.length === 0) {
      return null;
    }

    // 返回第一个场景
    return scenes[0];
  }
}
