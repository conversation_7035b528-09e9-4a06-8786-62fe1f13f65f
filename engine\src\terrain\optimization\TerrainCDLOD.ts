/**
 * 地形连续距离相关LOD（CDLOD）系统
 * 实现基于连续距离的LOD算法，提供平滑的LOD过渡
 */
import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { type Camera  } from '../../rendering/Camera';
import { TerrainComponent } from '../components/TerrainComponent';
import { TerrainChunk } from '../TerrainChunkSystem';
import { Debug } from '../../utils/Debug';
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 地形CDLOD配置接口
 */
export interface TerrainCDLODOptions {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否自动更新 */
  autoUpdate?: boolean;
  /** 更新频率 */
  updateFrequency?: number;
  /** 是否使用视锥体剔除 */
  useFrustumCulling?: boolean;
  /** 四叉树最大深度 */
  maxQuadTreeDepth?: number;
  /** 基础块大小 */
  baseChunkSize?: number;
  /** 过渡区域大小比例 */
  morphRegionRatio?: number;
  /** LOD级别距离因子 */
  lodDistanceFactor?: number;
  /** 是否使用GPU变形 */
  useGPUMorphing?: boolean;
  /** 是否使用调试可视化 */
  useDebugVisualization?: boolean;
}

/**
 * 四叉树节点接口
 */
export interface QuadTreeNode {
  /** 节点ID */
  id: string;
  /** 节点级别 */
  level: number;
  /** 节点边界 */
  bounds: THREE.Box2;
  /** 节点中心 */
  center: THREE.Vector2;
  /** 节点大小 */
  size: number;
  /** 子节点 */
  children: QuadTreeNode[] | null;
  /** 是否可见 */
  visible: boolean;
  /** 是否在过渡区域 */
  inMorphRegion: boolean;
  /** 过渡因子 */
  morphFactor: number;
  /** 地形块 */
  chunk: TerrainChunk | null;
}

/**
 * CDLOD事件类型
 */
export enum CDLODEventType {
  /** 节点可见性变更 */
  NODE_VISIBILITY_CHANGED = 'node_visibility_changed',
  /** 节点过渡因子变更 */
  NODE_MORPH_FACTOR_CHANGED = 'node_morph_factor_changed',
  /** 四叉树更新 */
  QUADTREE_UPDATED = 'quadtree_updated'
}

/**
 * 地形CDLOD系统
 */
export class TerrainCDLOD {
  /** 是否启用 */
  private enabled: boolean;
  /** 是否自动更新 */
  private autoUpdate: boolean;
  /** 更新频率 */
  private updateFrequency: number;
  /** 是否使用视锥体剔除 */
  private useFrustumCulling: boolean;
  /** 四叉树最大深度 */
  private maxQuadTreeDepth: number;
  /** 基础块大小 */
  private baseChunkSize: number;
  /** 过渡区域大小比例 */
  private morphRegionRatio: number;
  /** LOD级别距离因子 */
  private lodDistanceFactor: number;
  /** 是否使用GPU变形 */
  private useGPUMorphing: boolean;
  /** 是否使用调试可视化 */
  private useDebugVisualization: boolean;

  /** 地形实体映射 */
  private terrainEntities: Map<Entity, TerrainComponent>;
  /** 四叉树根节点映射 */
  private quadTrees: Map<string, QuadTreeNode>;
  /** 视锥体 */
  private frustum: THREE.Frustum;
  /** 帧计数器 */
  private frameCount: number;
  /** 事件发射器 */
  private eventEmitter: EventEmitter;
  /** 调试网格 */
  private debugMeshes: THREE.Mesh[];
  /** 实体管理器 */
  private entityManager: any;
  /** 活跃相机 */
  private activeCamera: Camera | null = null;
  /** 活跃场景 */
  private activeScene: any = null;

  /**
   * 创建地形CDLOD系统
   * @param options 配置选项
   */
  constructor(options: TerrainCDLODOptions = {}) {
    this.enabled = options.enabled !== undefined ? options.enabled : true;
    this.autoUpdate = options.autoUpdate !== undefined ? options.autoUpdate : true;
    this.updateFrequency = options.updateFrequency || 1;
    this.useFrustumCulling = options.useFrustumCulling !== undefined ? options.useFrustumCulling : true;
    this.maxQuadTreeDepth = options.maxQuadTreeDepth || 6;
    this.baseChunkSize = options.baseChunkSize || 64;
    this.morphRegionRatio = options.morphRegionRatio || 0.3;
    this.lodDistanceFactor = options.lodDistanceFactor || 2.0;
    this.useGPUMorphing = options.useGPUMorphing !== undefined ? options.useGPUMorphing : true;
    this.useDebugVisualization = options.useDebugVisualization !== undefined ? options.useDebugVisualization : false;

    this.terrainEntities = new Map();
    this.quadTrees = new Map();
    this.frustum = new THREE.Frustum();
    this.frameCount = 0;
    this.eventEmitter = new EventEmitter();
    this.debugMeshes = [];
  }

  /**
   * 添加地形实体
   * @param entity 实体
   * @param component 地形组件
   */
  public addTerrainEntity(entity: Entity, component: TerrainComponent): void {
    this.terrainEntities.set(entity, component);
    this.createQuadTreeForTerrain(entity, component);
  }

  /**
   * 移除地形实体
   * @param entity 实体
   */
  public removeTerrainEntity(entity: Entity): void {
    this.terrainEntities.delete(entity);
    this.quadTrees.delete(entity.id);
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.enabled || !this.autoUpdate) {
      return;
    }

    // 按照更新频率更新
    this.frameCount++;
    if (this.frameCount % this.updateFrequency !== 0) {
      return;
    }

    // 获取相机
    const camera = this.getCamera();
    if (!camera) {
      return;
    }

    // 更新视锥体
    if (this.useFrustumCulling) {
      this.updateFrustum(camera);
    }

    // 更新四叉树
    this.updateQuadTrees(camera);

    // 更新调试可视化
    if (this.useDebugVisualization) {
      this.updateDebugVisualization();
    }
  }

  /**
   * 获取相机
   * @returns 相机
   */
  private getCamera(): Camera | null {
    // 如果已经有活跃相机，则返回
    if (this.activeCamera) {
      return this.activeCamera;
    }

    // 否则尝试从实体管理器获取
    if (this.entityManager) {
      const cameras = this.entityManager.getComponentsOfType<Camera>('Camera');
      if (cameras.length > 0) {
        this.activeCamera = cameras[0];
        return this.activeCamera;
      }
    }

    return null;
  }

  /**
   * 更新视锥体
   * @param camera 相机
   */
  private updateFrustum(camera: Camera): void {
    const threeCamera = camera.getThreeCamera();
    const projScreenMatrix = new THREE.Matrix4();
    projScreenMatrix.multiplyMatrices(threeCamera.projectionMatrix, threeCamera.matrixWorldInverse);
    this.frustum.setFromProjectionMatrix(projScreenMatrix);
  }

  /**
   * 更新四叉树
   * @param camera 相机
   */
  private updateQuadTrees(camera: Camera): void {
    const cameraPosition = camera.getThreeCamera().position;

    // 遍历所有四叉树
    for (const [entityId, rootNode] of this.quadTrees.entries()) {
      this.updateQuadTreeNode(rootNode, cameraPosition);
    }

    // 发出四叉树更新事件
    this.eventEmitter.emit(CDLODEventType.QUADTREE_UPDATED);
  }

  /**
   * 更新调试可视化
   */
  private updateDebugVisualization(): void {
    // 清除现有的调试网格
    this.clearDebugMeshes();

    // 如果没有场景，则返回
    const scene = this.getScene();
    if (!scene) {
      return;
    }

    // 创建材质
    const visibleMaterial = new THREE.MeshBasicMaterial({
      color: 0x00ff00,
      wireframe: true,
      transparent: true,
      opacity: 0.5
    });

    const hiddenMaterial = new THREE.MeshBasicMaterial({
      color: 0xff0000,
      wireframe: true,
      transparent: true,
      opacity: 0.2
    });

    const morphMaterial = new THREE.MeshBasicMaterial({
      color: 0x0000ff,
      wireframe: true,
      transparent: true,
      opacity: 0.5
    });

    // 遍历所有四叉树
    for (const [entityId, rootNode] of this.quadTrees.entries()) {
      this.visualizeQuadTreeNode(rootNode, scene, visibleMaterial, hiddenMaterial, morphMaterial);
    }
  }

  /**
   * 可视化四叉树节点
   * @param node 节点
   * @param scene 场景
   * @param visibleMaterial 可见材质
   * @param hiddenMaterial 隐藏材质
   * @param morphMaterial 过渡材质
   */
  private visualizeQuadTreeNode(
    node: QuadTreeNode,
    scene: any,
    visibleMaterial: THREE.Material,
    hiddenMaterial: THREE.Material,
    morphMaterial: THREE.Material
  ): void {
    // 创建平面几何体
    const geometry = new THREE.PlaneGeometry(node.size, node.size);

    // 选择材质
    let material;
    if (node.visible) {
      if (node.inMorphRegion) {
        material = morphMaterial;
      } else {
        material = visibleMaterial;
      }
    } else {
      material = hiddenMaterial;
    }

    // 创建网格
    const mesh = new THREE.Mesh(geometry, material);

    // 设置位置（假设y轴为高度）
    (mesh as any).setPosition(node.center.x, 1, node.center.y);
    mesh.rotation.x = -Math.PI / 2;

    // 添加到场景
    scene.getThreeScene().add(mesh);

    // 存储调试网格
    this.debugMeshes.push(mesh);

    // 递归可视化子节点
    if (node.children) {
      for (const childNode of node.children) {
        this.visualizeQuadTreeNode(childNode, scene, visibleMaterial, hiddenMaterial, morphMaterial);
      }
    }
  }

  /**
   * 清除调试网格
   */
  private clearDebugMeshes(): void {
    // 获取场景
    const scene = this.getScene();
    if (!scene) {
      return;
    }

    // 移除所有调试网格
    for (const mesh of this.debugMeshes) {
      scene.getThreeScene().remove(mesh);
      (mesh.geometry as any).dispose();
      if (Array.isArray(mesh.material)) {
        for (const material of mesh.material) {
          (material as any).dispose();
        }
      } else {
        (mesh.material as any).dispose();
      }
    }

    // 清空数组
    this.debugMeshes = [];
  }

  /**
   * 获取场景
   * @returns 场景
   */
  private getScene(): any {
    // 如果已经有活跃场景，则返回
    if (this.activeScene) {
      return this.activeScene;
    }

    // 否则尝试从实体管理器获取
    if (this.entityManager) {
      const scenes = this.entityManager.getComponentsOfType<any>('Scene');
      if (scenes.length > 0) {
        this.activeScene = scenes[0];
        return this.activeScene;
      }
    }

    return null;
  }

  /**
   * 注册事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public on(event: CDLODEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public off(event: CDLODEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }

  /**
   * 设置实体管理器
   * @param entityManager 实体管理器
   */
  public setEntityManager(entityManager: any): void {
    // 存储实体管理器引用
    this.entityManager = entityManager;
  }

  /**
   * 设置活跃相机
   * @param camera 相机
   */
  public setActiveCamera(camera: Camera): void {
    this.activeCamera = camera;
  }

  /**
   * 设置活跃场景
   * @param scene 场景
   */
  public setActiveScene(scene: any): void {
    this.activeScene = scene;
  }

  /**
   * 更新四叉树节点
   * @param node 节点
   * @param cameraPosition 相机位置
   * @returns 是否应该渲染该节点
   */
  private updateQuadTreeNode(node: QuadTreeNode, cameraPosition: THREE.Vector3): boolean {
    // 计算节点中心的3D位置（假设y轴为高度）
    const nodeCenterWorld = new THREE.Vector3(node.center.x, 0, node.center.y);

    // 计算相机到节点中心的距离
    const distance = cameraPosition.distanceTo(nodeCenterWorld);

    // 计算该级别的最大可见距离
    const maxVisibleDistance = this.baseChunkSize * Math.pow(this.lodDistanceFactor, node.level);

    // 计算过渡区域的开始距离
    const morphStartDistance = maxVisibleDistance * (1 - this.morphRegionRatio);

    // 检查节点是否在视锥体内
    const boundingSphere = new THREE.Sphere(nodeCenterWorld, node.size * 0.5);
    const isInFrustum = !this.useFrustumCulling || this.frustum.intersectsSphere(boundingSphere);

    // 如果节点不在视锥体内，则隐藏
    if (!isInFrustum) {
      this.setNodeVisible(node, false);
      return false;
    }

    // 如果是叶子节点或距离大于最大可见距离，则显示该节点
    if (!node.children || distance > maxVisibleDistance) {
      this.setNodeVisible(node, true);

      // 计算过渡因子
      if (distance > morphStartDistance && distance < maxVisibleDistance) {
        const morphFactor = (distance - morphStartDistance) / (maxVisibleDistance - morphStartDistance);
        this.setNodeMorphFactor(node, morphFactor);
      } else {
        this.setNodeMorphFactor(node, 0);
      }

      return true;
    }

    // 否则，递归更新子节点
    let anyChildVisible = false;
    for (const childNode of node.children) {
      const childVisible = this.updateQuadTreeNode(childNode, cameraPosition);
      anyChildVisible = anyChildVisible || childVisible;
    }

    // 如果所有子节点都不可见，则显示该节点
    if (!anyChildVisible) {
      this.setNodeVisible(node, true);
      return true;
    }

    // 否则隐藏该节点
    this.setNodeVisible(node, false);
    return false;
  }

  /**
   * 设置节点可见性
   * @param node 节点
   * @param visible 是否可见
   */
  private setNodeVisible(node: QuadTreeNode, visible: boolean): void {
    // 如果可见性没有变化，则返回
    if (node.visible === visible) {
      return;
    }

    // 更新可见性
    node.visible = visible;

    // 发出节点可见性变更事件
    this.eventEmitter.emit(CDLODEventType.NODE_VISIBILITY_CHANGED, node, visible);
  }

  /**
   * 设置节点过渡因子
   * @param node 节点
   * @param factor 过渡因子
   */
  private setNodeMorphFactor(node: QuadTreeNode, factor: number): void {
    // 如果过渡因子没有变化，则返回
    if (Math.abs(node.morphFactor - factor) < 0.01) {
      return;
    }

    // 更新过渡因子
    node.morphFactor = factor;
    node.inMorphRegion = factor > 0;

    // 发出节点过渡因子变更事件
    this.eventEmitter.emit(CDLODEventType.NODE_MORPH_FACTOR_CHANGED, node, factor);
  }

  /**
   * 为地形创建四叉树
   * @param entity 实体
   * @param component 地形组件
   */
  private createQuadTreeForTerrain(entity: Entity, component: TerrainComponent): void {
    // 创建根节点
    const rootNode: QuadTreeNode = {
      id: `${entity.id}_root`,
      level: 0,
      bounds: new THREE.Box2(
        new THREE.Vector2(0, 0),
        new THREE.Vector2(component.width, component.height)
      ),
      center: new THREE.Vector2(component.width / 2, component.height / 2),
      size: Math.max(component.width, component.height),
      children: null,
      visible: true,
      inMorphRegion: false,
      morphFactor: 0,
      chunk: null
    };

    // 递归构建四叉树
    this.buildQuadTree(rootNode, entity, component, 0);

    // 存储四叉树
    this.quadTrees.set(entity.id, rootNode);
  }

  /**
   * 递归构建四叉树
   * @param node 当前节点
   * @param entity 实体
   * @param component 地形组件
   * @param level 当前级别
   */
  private buildQuadTree(
    node: QuadTreeNode,
    entity: Entity,
    component: TerrainComponent,
    level: number
  ): void {
    // 如果达到最大深度，则停止
    if (level >= this.maxQuadTreeDepth) {
      return;
    }

    // 创建子节点
    node.children = [];

    // 计算子节点大小
    const halfSize = node.size / 2;
    const quarterSize = halfSize / 2;

    // 创建四个子节点（西北、东北、西南、东南）
    const childNodes: QuadTreeNode[] = [
      // 西北
      {
        id: `${node.id}_nw`,
        level: level + 1,
        bounds: new THREE.Box2(
          new THREE.Vector2(node.bounds.min.x, node.bounds.min.y),
          new THREE.Vector2(node.bounds.min.x + halfSize, node.bounds.min.y + halfSize)
        ),
        center: new THREE.Vector2(node.bounds.min.x + quarterSize, node.bounds.min.y + quarterSize),
        size: halfSize,
        children: null,
        visible: false,
        inMorphRegion: false,
        morphFactor: 0,
        chunk: null
      },
      // 东北
      {
        id: `${node.id}_ne`,
        level: level + 1,
        bounds: new THREE.Box2(
          new THREE.Vector2(node.bounds.min.x + halfSize, node.bounds.min.y),
          new THREE.Vector2(node.bounds.max.x, node.bounds.min.y + halfSize)
        ),
        center: new THREE.Vector2(node.bounds.min.x + halfSize + quarterSize, node.bounds.min.y + quarterSize),
        size: halfSize,
        children: null,
        visible: false,
        inMorphRegion: false,
        morphFactor: 0,
        chunk: null
      },
      // 西南
      {
        id: `${node.id}_sw`,
        level: level + 1,
        bounds: new THREE.Box2(
          new THREE.Vector2(node.bounds.min.x, node.bounds.min.y + halfSize),
          new THREE.Vector2(node.bounds.min.x + halfSize, node.bounds.max.y)
        ),
        center: new THREE.Vector2(node.bounds.min.x + quarterSize, node.bounds.min.y + halfSize + quarterSize),
        size: halfSize,
        children: null,
        visible: false,
        inMorphRegion: false,
        morphFactor: 0,
        chunk: null
      },
      // 东南
      {
        id: `${node.id}_se`,
        level: level + 1,
        bounds: new THREE.Box2(
          new THREE.Vector2(node.bounds.min.x + halfSize, node.bounds.min.y + halfSize),
          new THREE.Vector2(node.bounds.max.x, node.bounds.max.y)
        ),
        center: new THREE.Vector2(node.bounds.min.x + halfSize + quarterSize, node.bounds.min.y + halfSize + quarterSize),
        size: halfSize,
        children: null,
        visible: false,
        inMorphRegion: false,
        morphFactor: 0,
        chunk: null
      }
    ];

    // 添加子节点
    node.children = childNodes;

    // 递归构建子节点
    for (const childNode of childNodes) {
      this.buildQuadTree(childNode, entity, component, level + 1);
    }
  }
}
