/**
 * 瀑布组件
 * 用于表示瀑布及其特殊物理属性和效果
 */
import * as THREE from 'three';
import { WaterBodyComponent, WaterBodyType } from './WaterBodyComponent';
import type { Entity } from '../../core/Entity';
import { Debug } from '../../utils/Debug';
import { AudioSource, AudioSourceOptions } from '../../audio/AudioSource';
import { AudioType } from '../../audio/AudioSystem';
import { UnderwaterParticleSystem, UnderwaterParticleType } from '../../rendering/water/UnderwaterParticleSystem';
import { WaterInstancedRenderer, WaterEffectType } from '../../rendering/water/WaterInstancedRenderer';
import { SimplexNoise } from '../../utils/SimplexNoise';

/**
 * 瀑布类型
 */
export enum WaterfallType {
  /** 标准瀑布 */
  STANDARD = 'standard',
  /** 高瀑布 */
  HIGH = 'high',
  /** 宽瀑布 */
  WIDE = 'wide',
  /** 多级瀑布 */
  MULTI_LEVEL = 'multi_level',
  /** 小瀑布 */
  SMALL = 'small',
  /** 山涧瀑布 */
  MOUNTAIN = 'mountain',
  /** 薄纱瀑布 */
  VEIL = 'veil',
  /** 喷泉 */
  FOUNTAIN = 'fountain'
}

/**
 * 瀑布配置
 */
export interface WaterfallConfig {
  /** 瀑布类型 */
  type?: WaterfallType;
  /** 瀑布宽度 */
  width?: number;
  /** 瀑布高度 */
  height?: number;
  /** 瀑布深度 */
  depth?: number;
  /** 瀑布位置 */
  position?: THREE.Vector3;
  /** 瀑布旋转 */
  rotation?: THREE.Euler;
  /** 瀑布颜色 */
  color?: THREE.Color;
  /** 瀑布不透明度 */
  opacity?: number;
  /** 瀑布流速 */
  flowSpeed?: number;
  /** 瀑布流向 */
  flowDirection?: THREE.Vector3;
  /** 瀑布湍流强度 */
  turbulenceStrength?: number;
  /** 瀑布湍流频率 */
  turbulenceFrequency?: number;
  /** 瀑布湍流速度 */
  turbulenceSpeed?: number;
  /** 是否启用水雾效果 */
  enableMistEffect?: boolean;
  /** 水雾效果强度 */
  mistEffectStrength?: number;
  /** 是否启用水花效果 */
  enableSplashEffect?: boolean;
  /** 水花效果强度 */
  splashEffectStrength?: number;
  /** 是否启用水滴效果 */
  enableDropletEffect?: boolean;
  /** 水滴效果强度 */
  dropletEffectStrength?: number;
  /** 是否启用声音效果 */
  enableSoundEffect?: boolean;
  /** 声音效果音量 */
  soundEffectVolume?: number;
  /** 是否启用水流动力学 */
  enableFluidDynamics?: boolean;
}

/**
 * 瀑布组件
 */
export class WaterfallComponent extends WaterBodyComponent {
  /** 瀑布类型 */
  private waterfallType: WaterfallType = WaterfallType.STANDARD;
  /** 瀑布湍流强度 */
  private turbulenceStrength: number = 1.0;
  /** 瀑布湍流频率 */
  private turbulenceFrequency: number = 2.0;
  /** 瀑布湍流速度 */
  private turbulenceSpeed: number = 1.0;
  /** 是否启用水雾效果 */
  private enableMistEffect: boolean = true;
  /** 水雾效果强度 */
  private mistEffectStrength: number = 1.0;
  /** 是否启用水花效果 */
  private enableSplashEffect: boolean = true;
  /** 水花效果强度 */
  private splashEffectStrength: number = 1.0;
  /** 是否启用水滴效果 */
  private enableDropletEffect: boolean = true;
  /** 水滴效果强度 */
  private dropletEffectStrength: number = 1.0;
  /** 是否启用声音效果 */
  private enableSoundEffect: boolean = true;
  /** 声音效果音量 */
  private soundEffectVolume: number = 1.0;
  /** 是否启用水流动力学 */
  private enableFluidDynamics: boolean = true;
  /** 音频源 */
  private audioSource: AudioSource | null = null;
  /** 水雾粒子系统 */
  private mistParticleSystem: any = null;
  /** 水花粒子系统 */
  private splashParticleSystem: any = null;
  /** 水滴粒子系统 */
  private dropletParticleSystem: any = null;
  /** 水流路径点 */
  private flowPathPoints: THREE.Vector3[] = [];
  /** 水流网格 */
  private flowMesh: THREE.Mesh | null = null;
  /** 底部水体 */
  private bottomWaterBody: WaterBodyComponent | null = null;
  /** 噪声生成器 */
  private noiseGenerator: SimplexNoise = new SimplexNoise();

  /**
   * 创建瀑布组件
   * @param entity 实体
   * @param config 瀑布配置
   */
  constructor(entity: Entity, config: WaterfallConfig = {}) {
    super(entity);

    // 设置水体类型为瀑布
    this.type = WaterBodyType.WATERFALL;

    // 应用配置
    this.applyConfig(config);

    // 初始化瀑布特有属性
    this.initialize();
  }

  /**
   * 获取瀑布类型
   * @returns 瀑布类型
   */
  public getWaterfallType(): WaterfallType {
    return this.waterfallType;
  }

  /**
   * 设置瀑布类型
   * @param type 瀑布类型
   */
  public setWaterfallType(type: WaterfallType): void {
    this.waterfallType = type;
  }

  /**
   * 应用配置
   * @param config 瀑布配置
   */
  private applyConfig(config: WaterfallConfig): void {
    // 应用瀑布类型
    if (config.type !== undefined) this.waterfallType = config.type;

    // 应用基本配置
    if (config.width !== undefined) this.size.width = config.width;
    if (config.height !== undefined) this.size.height = config.height;
    if (config.depth !== undefined) this.size.depth = config.depth;
    if (config.position) this.position.copy(config.position);
    if (config.rotation) this.rotation.copy(config.rotation);
    if (config.color) this.setColor(config.color);
    if (config.opacity !== undefined) this.setOpacity(config.opacity);
    if (config.flowSpeed !== undefined) this.setFlowSpeed(config.flowSpeed);
    if (config.flowDirection) this.setFlowDirection(config.flowDirection);

    // 应用瀑布特有配置
    if (config.turbulenceStrength !== undefined) this.turbulenceStrength = config.turbulenceStrength;
    if (config.turbulenceFrequency !== undefined) this.turbulenceFrequency = config.turbulenceFrequency;
    if (config.turbulenceSpeed !== undefined) this.turbulenceSpeed = config.turbulenceSpeed;
    if (config.enableMistEffect !== undefined) this.enableMistEffect = config.enableMistEffect;
    if (config.mistEffectStrength !== undefined) this.mistEffectStrength = config.mistEffectStrength;
    if (config.enableSplashEffect !== undefined) this.enableSplashEffect = config.enableSplashEffect;
    if (config.splashEffectStrength !== undefined) this.splashEffectStrength = config.splashEffectStrength;
    if (config.enableDropletEffect !== undefined) this.enableDropletEffect = config.enableDropletEffect;
    if (config.dropletEffectStrength !== undefined) this.dropletEffectStrength = config.dropletEffectStrength;
    if (config.enableSoundEffect !== undefined) this.enableSoundEffect = config.enableSoundEffect;
    if (config.soundEffectVolume !== undefined) this.soundEffectVolume = config.soundEffectVolume;
    if (config.enableFluidDynamics !== undefined) this.enableFluidDynamics = config.enableFluidDynamics;
  }

  /**
   * 初始化瀑布组件
   */
  public initialize(): void {
    // 调用父类初始化
    super.initialize();

    // 设置默认流向为向下
    if (!this.flowDirection) {
      this.setFlowDirection(new THREE.Vector3(0, -1, 0));
    }

    // 设置默认流速
    if (this.flowSpeed === 0) {
      this.setFlowSpeed(2.0);
    }

    // 计算水流路径
    this.calculateFlowPath();

    // 创建水流网格
    this.createFlowMesh();

    // 初始化音频
    this.initializeAudio();

    // 初始化粒子系统
    this.initializeParticleSystems();

    // 创建底部水体
    this.createBottomWaterBody();

    Debug.log('WaterfallComponent', '瀑布组件初始化完成');
  }

  /**
   * 计算水流路径
   * 根据瀑布的位置、旋转和尺寸计算水流的路径点
   */
  private calculateFlowPath(): void {
    // 清空路径点
    this.flowPathPoints = [];

    // 获取瀑布的位置和旋转
    const position = this.getPosition();
    const rotation = this.getRotation();

    // 创建变换矩阵
    const matrix = new THREE.Matrix4();
    matrix.makeRotationFromEuler(rotation);
    matrix.setPosition(position);

    // 计算瀑布顶部中心点
    const topCenter = new THREE.Vector3(0, this.size.height / 2, 0);
    topCenter.applyMatrix4(matrix);
    this.flowPathPoints.push(topCenter);

    // 计算瀑布底部中心点
    const bottomCenter = new THREE.Vector3(0, -this.size.height / 2, 0);
    bottomCenter.applyMatrix4(matrix);
    this.flowPathPoints.push(bottomCenter);

    // 计算落水点（考虑瀑布的倾斜角度）
    const flowDirection = this.getFlowDirection();
    const flowDistance = this.size.height * 2; // 假设水流可以流动的距离是瀑布高度的2倍
    const landingPoint = new THREE.Vector3().copy(bottomCenter).add(
      new THREE.Vector3().copy(flowDirection).multiplyScalar(flowDistance)
    );
    this.flowPathPoints.push(landingPoint);

    Debug.log('WaterfallComponent', `计算水流路径: ${this.flowPathPoints.length}个点`);
  }

  /**
   * 创建水流网格
   * 根据水流路径创建水流的网格
   */
  private createFlowMesh(): void {
    // 如果路径点不足，则不创建网格
    if (this.flowPathPoints.length < 2) return;

    // 创建曲线
    const curve = new THREE.CatmullRomCurve3(this.flowPathPoints);

    // 创建管道几何体
    const geometry = new THREE.TubeGeometry(
      curve,
      20, // 分段数
      this.size.width / 2, // 管道半径
      8, // 管道周围分段数
      false // 是否闭合
    );

    // 创建水流材质
    const material = new THREE.MeshStandardMaterial({
      color: this.getColor(),
      transparent: true,
      opacity: this.getOpacity(),
      side: THREE.DoubleSide,
      roughness: 0.1,
      metalness: 0.2
    });

    // 创建网格
    this.flowMesh = new THREE.Mesh(geometry, material);

    // 添加到实体
    this.entity.addObject(this.flowMesh);

    Debug.log('WaterfallComponent', '创建水流网格完成');
  }

  /**
   * 初始化音频
   * 为瀑布创建音频源并播放瀑布声音
   */
  private initializeAudio(): void {
    // 如果未启用声音效果，则不初始化音频
    if (!this.enableSoundEffect) return;

    // 获取音频系统
    const audioSystem = this.entity.getWorld().getSystem('AudioSystem');
    if (!audioSystem) return;

    // 创建音频源
    const audioSourceId = `waterfall_${this.entity.id}`;
    this.audioSource = audioSystem.createSource(audioSourceId, AudioType.AMBIENT);
    if (!this.audioSource) return;

    // 设置音频源属性
    this.audioSource.setVolume(this.soundEffectVolume);
    this.audioSource.setLoop(true);
    this.audioSource.setSpatial(true);
    this.audioSource.setPosition(this.getPosition());
    this.audioSource.setRefDistance(10);
    this.audioSource.setMaxDistance(100);
    this.audioSource.setRolloffFactor(1);

    // 播放瀑布声音
    audioSystem.play(audioSourceId, 'sounds/waterfall.mp3', {
      loop: true,
      volume: this.soundEffectVolume
    });

    Debug.log('WaterfallComponent', '初始化音频完成');
  }

  /**
   * 初始化粒子系统
   * 为瀑布创建水雾、水花和水滴粒子效果
   */
  private initializeParticleSystems(): void {
    // 如果未启用任何效果，则不初始化粒子系统
    if (!this.enableMistEffect && !this.enableSplashEffect && !this.enableDropletEffect) return;

    // 获取水下粒子系统
    const underwaterParticleSystem = this.entity.getWorld().getSystem('UnderwaterParticleSystem');
    if (!underwaterParticleSystem) return;

    // 获取瀑布底部位置（用于水花效果）
    const bottomPosition = this.getBottomPosition();

    // 获取瀑布顶部位置（用于水滴效果）
    const topPosition = this.getTopPosition();

    // 获取瀑布中间位置（用于水雾效果）
    const middlePosition = new THREE.Vector3().addVectors(topPosition, bottomPosition).multiplyScalar(0.5);

    // 创建水雾效果
    if (this.enableMistEffect) {
      underwaterParticleSystem.addParticleGroup(
        this.entity.id,
        'waterfallMist',
        {
          type: UnderwaterParticleType.MIST,
          count: Math.floor(200 * this.mistEffectStrength),
          size: [0.5, 1.5],
          color: 0xffffff,
          opacity: 0.3 * this.mistEffectStrength,
          lifetime: [2, 5],
          speed: [0.1, 0.2],
          acceleration: new THREE.Vector3(0, 0.05, 0),
          blending: THREE.AdditiveBlending,
          emissionArea: {
            shape: 'box',
            size: new THREE.Vector3(this.size.width, this.size.height / 2, this.size.width),
            position: bottomPosition
          }
        }
      );
    }

    // 创建水花效果
    if (this.enableSplashEffect) {
      underwaterParticleSystem.addParticleGroup(
        this.entity.id,
        'waterfallSplash',
        {
          type: UnderwaterParticleType.SPLASH,
          count: Math.floor(100 * this.splashEffectStrength),
          size: [0.1, 0.3],
          color: 0xffffff,
          opacity: 0.7 * this.splashEffectStrength,
          lifetime: [0.5, 1.5],
          speed: [0.5, 2.0],
          acceleration: new THREE.Vector3(0, -9.8, 0),
          rotation: true,
          rotationSpeed: [1.0, 3.0],
          blending: THREE.AdditiveBlending,
          emissionArea: {
            shape: 'circle',
            radius: this.size.width / 2,
            position: bottomPosition
          }
        }
      );
    }

    // 创建水滴效果
    if (this.enableDropletEffect) {
      underwaterParticleSystem.addParticleGroup(
        this.entity.id,
        'waterfallDroplet',
        {
          type: UnderwaterParticleType.DROPLET,
          count: Math.floor(150 * this.dropletEffectStrength),
          size: [0.05, 0.15],
          color: 0xffffff,
          opacity: 0.8 * this.dropletEffectStrength,
          lifetime: [0.8, 1.2],
          speed: [1.0, 3.0],
          acceleration: new THREE.Vector3(0, -9.8, 0),
          rotation: true,
          rotationSpeed: [2.0, 5.0],
          blending: THREE.AdditiveBlending,
          emissionArea: {
            shape: 'box',
            size: new THREE.Vector3(this.size.width, this.size.height / 4, this.size.depth),
            position: middlePosition
          }
        }
      );
    }

    Debug.log('WaterfallComponent', '初始化粒子系统完成');
  }

  /**
   * 创建底部水体
   * 在瀑布底部创建一个水池
   */
  private createBottomWaterBody(): void {
    // 获取瀑布底部位置
    const bottomPosition = this.getBottomPosition();

    // 创建底部水体实体
    const bottomWaterEntity = new Entity();
    bottomWaterEntity.setName(`${this.entity.getName()}_bottom_water`);
    bottomWaterEntity.setParent(this.entity);

    // 创建底部水体组件
    this.bottomWaterBody = new WaterBodyComponent(bottomWaterEntity);
    this.bottomWaterBody.setType(WaterBodyType.LAKE);
    this.bottomWaterBody.setSize({
      width: this.size.width * 3,
      height: this.size.height / 4,
      depth: this.size.width * 3
    });
    this.bottomWaterBody.setPosition(bottomPosition);
    this.bottomWaterBody.setColor(this.getColor());
    this.bottomWaterBody.setOpacity(this.getOpacity());

    // 设置波动参数
    this.bottomWaterBody.setWaveParams({
      amplitude: 0.1,
      frequency: 1.0,
      speed: 0.5,
      direction: new THREE.Vector2(1, 1)
    });

    // 初始化底部水体
    this.bottomWaterBody.initialize();

    // 添加到世界
    this.entity.getWorld().addEntity(bottomWaterEntity);

    Debug.log('WaterfallComponent', '创建底部水体完成');
  }

  /**
   * 获取瀑布顶部位置
   * @returns 瀑布顶部位置
   */
  private getTopPosition(): THREE.Vector3 {
    // 如果有流路径点，则使用第一个点
    if (this.flowPathPoints.length > 0) {
      return this.flowPathPoints[0].clone();
    }

    // 否则根据瀑布位置和旋转计算顶部位置
    const position = this.getPosition();
    const rotation = this.getRotation();

    // 创建变换矩阵
    const matrix = new THREE.Matrix4();
    matrix.makeRotationFromEuler(rotation);
    matrix.setPosition(position);

    // 计算瀑布顶部中心点
    const topCenter = new THREE.Vector3(0, this.size.height / 2, 0);
    topCenter.applyMatrix4(matrix);

    return topCenter;
  }

  /**
   * 获取瀑布底部位置
   * @returns 瀑布底部位置
   */
  private getBottomPosition(): THREE.Vector3 {
    // 如果有流路径点，则使用最后一个点
    if (this.flowPathPoints.length > 0) {
      return this.flowPathPoints[this.flowPathPoints.length - 1].clone();
    }

    // 否则根据瀑布位置和旋转计算底部位置
    const position = this.getPosition();
    const rotation = this.getRotation();
    const flowDirection = this.getFlowDirection();
    const flowDistance = this.size.height * 2;

    // 创建变换矩阵
    const matrix = new THREE.Matrix4();
    matrix.makeRotationFromEuler(rotation);
    matrix.setPosition(position);

    // 计算瀑布底部中心点
    const bottomCenter = new THREE.Vector3(0, -this.size.height / 2, 0);
    bottomCenter.applyMatrix4(matrix);

    // 计算落水点
    return new THREE.Vector3().copy(bottomCenter).add(
      new THREE.Vector3().copy(flowDirection).multiplyScalar(flowDistance)
    );
  }

  /**
   * 更新瀑布组件
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 调用父类更新
    super.update(deltaTime);

    // 更新水流动力学
    if (this.enableFluidDynamics) {
      this.updateFluidDynamics(deltaTime);
    }

    // 更新音频
    this.updateAudio(deltaTime);

    // 更新粒子效果
    this.updateParticleEffects(deltaTime);
  }

  /**
   * 更新水流动力学
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateFluidDynamics(deltaTime: number): void {
    // 如果没有水流网格，则不更新
    if (!this.flowMesh) return;

    // 获取水流材质
    const material = this.flowMesh.material as THREE.MeshStandardMaterial;
    if (!material) return;

    // 更新水流材质的位移贴图偏移，模拟水流动态
    if (!material.userData.time) {
      material.userData.time = 0;
    }
    material.userData.time += deltaTime * this.flowSpeed;

    // 如果材质有位移贴图，更新偏移
    if (material.displacementMap) {
      material.displacementMap.offset.y = -material.userData.time;
    }

    // 如果材质有法线贴图，更新偏移
    if (material.normalMap) {
      material.normalMap.offset.y = -material.userData.time;
    }

    // 应用湍流效果
    this.applyTurbulence(deltaTime);
  }

  /**
   * 应用湍流效果
   * @param deltaTime 帧间隔时间（秒）
   */
  private applyTurbulence(deltaTime: number): void {
    // 如果没有水流网格，则不应用湍流
    if (!this.flowMesh) return;

    // 获取几何体
    const geometry = this.flowMesh.geometry as THREE.BufferGeometry;
    if (!geometry || !geometry.attributes.position) return;

    // 获取顶点位置
    const positions = geometry.attributes.position.array;
    const count = positions.length / 3;

    // 如果没有原始位置，则保存原始位置
    if (!geometry.userData.originalPositions) {
      geometry.userData.originalPositions = new Float32Array(positions);
      geometry.userData.time = 0;
    }

    // 更新时间
    geometry.userData.time += deltaTime * this.turbulenceSpeed;
    const time = geometry.userData.time;

    // 应用湍流扰动
    for (let i = 0; i < count; i++) {
      const index = i * 3;
      const originalX = geometry.userData.originalPositions[index];
      const originalY = geometry.userData.originalPositions[index + 1];
      const originalZ = geometry.userData.originalPositions[index + 2];

      // 使用噪声函数模拟湍流
      const noise = this.simplex3D(
        originalX * this.turbulenceFrequency,
        originalY * this.turbulenceFrequency,
        time
      );

      // 应用扰动
      positions[index] = originalX + noise * this.turbulenceStrength * 0.1;
      positions[index + 2] = originalZ + noise * this.turbulenceStrength * 0.1;
    }

    // 更新几何体
    geometry.attributes.position.needsUpdate = true;
  }

  /**
   * 更新音频
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateAudio(deltaTime: number): void {
    // 如果未启用声音效果或没有音频源，则不更新
    if (!this.enableSoundEffect || !this.audioSource) return;

    // 更新音频源位置
    this.audioSource.setPosition(this.getPosition());

    // 根据瀑布大小和流速调整音量
    const volumeFactor = Math.min(1.0, (this.size.width * this.size.height * this.flowSpeed) / 100);
    this.audioSource.setVolume(this.soundEffectVolume * volumeFactor);
  }

  /**
   * 更新粒子效果
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateParticleEffects(deltaTime: number): void {
    // 如果未启用任何效果，则不更新
    if (!this.enableMistEffect && !this.enableSplashEffect && !this.enableDropletEffect) return;

    // 获取水下粒子系统
    const underwaterParticleSystem = this.entity.getWorld().getSystem('UnderwaterParticleSystem');
    if (!underwaterParticleSystem) return;

    // 获取瀑布底部位置
    const bottomPosition = this.getBottomPosition();

    // 获取瀑布顶部位置
    const topPosition = this.getTopPosition();

    // 获取瀑布中间位置
    const middlePosition = new THREE.Vector3().addVectors(topPosition, bottomPosition).multiplyScalar(0.5);

    // 更新水雾粒子位置
    if (this.enableMistEffect) {
      underwaterParticleSystem.updateParticleGroupPosition(
        this.entity.id,
        'waterfallMist',
        bottomPosition
      );

      // 根据流速调整水雾粒子发射率
      underwaterParticleSystem.updateParticleGroupEmissionRate(
        this.entity.id,
        'waterfallMist',
        Math.floor(200 * this.mistEffectStrength * (this.flowSpeed / 2.0))
      );
    }

    // 更新水花粒子位置
    if (this.enableSplashEffect) {
      underwaterParticleSystem.updateParticleGroupPosition(
        this.entity.id,
        'waterfallSplash',
        bottomPosition
      );

      // 根据流速调整水花粒子发射率
      underwaterParticleSystem.updateParticleGroupEmissionRate(
        this.entity.id,
        'waterfallSplash',
        Math.floor(100 * this.splashEffectStrength * (this.flowSpeed / 2.0))
      );
    }

    // 更新水滴粒子位置
    if (this.enableDropletEffect) {
      underwaterParticleSystem.updateParticleGroupPosition(
        this.entity.id,
        'waterfallDroplet',
        middlePosition
      );

      // 根据流速调整水滴粒子发射率
      underwaterParticleSystem.updateParticleGroupEmissionRate(
        this.entity.id,
        'waterfallDroplet',
        Math.floor(150 * this.dropletEffectStrength * (this.flowSpeed / 2.0))
      );
    }
  }

  /**
   * 简化版3D柏林噪声函数
   * @param x X坐标
   * @param y Y坐标
   * @param z Z坐标
   * @returns 噪声值（-1到1之间）
   */
  private simplex3D(x: number, y: number, z: number): number {
    // 这是一个简化版的柏林噪声函数，用于模拟湍流
    // 实际项目中应使用更高质量的噪声库，如simplex-noise.js
    const dot = (g: number[], x: number, y: number, z: number) => {
      return g[0] * x + g[1] * y + g[2] * z;
    };

    const grad3 = [
      [1, 1, 0], [-1, 1, 0], [1, -1, 0], [-1, -1, 0],
      [1, 0, 1], [-1, 0, 1], [1, 0, -1], [-1, 0, -1],
      [0, 1, 1], [0, -1, 1], [0, 1, -1], [0, -1, -1]
    ];

    const p = new Array(256);
    for (let i = 0; i < 256; i++) {
      p[i] = Math.floor(Math.random() * 256);
    }

    const perm = new Array(512);
    const permMod12 = new Array(512);
    for (let i = 0; i < 512; i++) {
      perm[i] = p[i & 255];
      permMod12[i] = perm[i] % 12;
    }

    const F3 = 1 / 3;
    const G3 = 1 / 6;

    const s = (x + y + z) * F3;
    const i = Math.floor(x + s);
    const j = Math.floor(y + s);
    const k = Math.floor(z + s);
    const t = (i + j + k) * G3;
    const X0 = i - t;
    const Y0 = j - t;
    const Z0 = k - t;
    const x0 = x - X0;
    const y0 = y - Y0;
    const z0 = z - Z0;

    let i1, j1, k1;
    let i2, j2, k2;
    if (x0 >= y0) {
      if (y0 >= z0) { i1 = 1; j1 = 0; k1 = 0; i2 = 1; j2 = 1; k2 = 0; }
      else if (x0 >= z0) { i1 = 1; j1 = 0; k1 = 0; i2 = 1; j2 = 0; k2 = 1; }
      else { i1 = 0; j1 = 0; k1 = 1; i2 = 1; j2 = 0; k2 = 1; }
    } else {
      if (y0 < z0) { i1 = 0; j1 = 0; k1 = 1; i2 = 0; j2 = 1; k2 = 1; }
      else if (x0 < z0) { i1 = 0; j1 = 1; k1 = 0; i2 = 0; j2 = 1; k2 = 1; }
      else { i1 = 0; j1 = 1; k1 = 0; i2 = 1; j2 = 1; k2 = 0; }
    }

    const x1 = x0 - i1 + G3;
    const y1 = y0 - j1 + G3;
    const z1 = z0 - k1 + G3;
    const x2 = x0 - i2 + 2 * G3;
    const y2 = y0 - j2 + 2 * G3;
    const z2 = z0 - k2 + 2 * G3;
    const x3 = x0 - 1 + 3 * G3;
    const y3 = y0 - 1 + 3 * G3;
    const z3 = z0 - 1 + 3 * G3;

    const ii = i & 255;
    const jj = j & 255;
    const kk = k & 255;
    const gi0 = permMod12[ii + perm[jj + perm[kk]]];
    const gi1 = permMod12[ii + i1 + perm[jj + j1 + perm[kk + k1]]];
    const gi2 = permMod12[ii + i2 + perm[jj + j2 + perm[kk + k2]]];
    const gi3 = permMod12[ii + 1 + perm[jj + 1 + perm[kk + 1]]];

    let t0 = 0.6 - x0 * x0 - y0 * y0 - z0 * z0;
    let n0 = t0 < 0 ? 0 : Math.pow(t0, 4) * dot(grad3[gi0], x0, y0, z0);
    let t1 = 0.6 - x1 * x1 - y1 * y1 - z1 * z1;
    let n1 = t1 < 0 ? 0 : Math.pow(t1, 4) * dot(grad3[gi1], x1, y1, z1);
    let t2 = 0.6 - x2 * x2 - y2 * y2 - z2 * z2;
    let n2 = t2 < 0 ? 0 : Math.pow(t2, 4) * dot(grad3[gi2], x2, y2, z2);
    let t3 = 0.6 - x3 * x3 - y3 * y3 - z3 * z3;
    let n3 = t3 < 0 ? 0 : Math.pow(t3, 4) * dot(grad3[gi3], x3, y3, z3);

    return 32 * (n0 + n1 + n2 + n3);
  }

  /**
   * 销毁瀑布组件
   */
  public destroy(): void {
    // 停止音频
    if (this.audioSource) {
      this.audioSource.stop();
      this.audioSource = null;
    }

    // 移除水流网格
    if (this.flowMesh) {
      this.entity.removeObject(this.flowMesh);
      (this.flowMesh.geometry as any).dispose();
      (this.flowMesh.material as THREE.Material).dispose();
      this.flowMesh = null;
    }

    // 移除底部水体
    if (this.bottomWaterBody) {
      const bottomWaterEntity = this.bottomWaterBody.getEntity();
      if (bottomWaterEntity) {
        this.entity.getWorld().removeEntity(bottomWaterEntity);
      }
      this.bottomWaterBody = null;
    }

    // 移除粒子系统
    const underwaterParticleSystem = this.entity.getWorld().getSystem('UnderwaterParticleSystem');
    if (underwaterParticleSystem) {
      underwaterParticleSystem.removeParticleGroup(this.entity.id, 'waterfallMist');
      underwaterParticleSystem.removeParticleGroup(this.entity.id, 'waterfallSplash');
    }

    // 调用父类销毁
    super.destroy();
  }
}
