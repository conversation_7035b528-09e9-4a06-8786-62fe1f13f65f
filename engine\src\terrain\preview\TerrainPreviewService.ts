/**
 * 地形预览服务
 * 用于在导入前预览地形效果
 */
import * as THREE from 'three';
import { TerrainComponent } from '../components/TerrainComponent';
import { HeightMapImportOptions } from '../io/HeightMapImportExport';
import { TerrainImportOptions } from '../io/TerrainImportExportService';
import { ThirdPartyTerrainFormat, ThirdPartyTerrainImportOptions } from '../io/ThirdPartyTerrainImportExport';
import { Debug } from '../../utils/Debug';

/**
 * 预览渲染器选项
 */
export interface PreviewRendererOptions {
  /** 宽度 */
  width: number;
  /** 高度 */
  height: number;
  /** 背景颜色 */
  backgroundColor?: number;
  /** 是否使用环境光遮蔽 */
  useAO?: boolean;
  /** 是否使用阴影 */
  useShadows?: boolean;
  /** 是否使用线框 */
  useWireframe?: boolean;
  /** 是否使用网格 */
  useGrid?: boolean;
}

/**
 * 地形预览服务
 */
export class TerrainPreviewService {
  /** 场景 */
  private scene: THREE.Scene;
  /** 相机 */
  private camera: THREE.PerspectiveCamera;
  /** 渲染器 */
  private renderer: THREE.WebGLRenderer;
  /** 地形网格 */
  private terrainMesh: THREE.Mesh | null = null;
  /** 地形材质 */
  private terrainMaterial: THREE.Material | null = null;
  /** 地形几何体 */
  private terrainGeometry: THREE.BufferGeometry | null = null;
  /** 网格辅助对象 */
  private gridHelper: THREE.GridHelper | null = null;
  /** 环境光 */
  private ambientLight: THREE.AmbientLight;
  /** 方向光 */
  private directionalLight: THREE.DirectionalLight;
  /** 是否已初始化 */
  private initialized: boolean = false;
  /** 渲染器选项 */
  private options: PreviewRendererOptions;
  
  /**
   * 创建地形预览服务
   * @param options 渲染器选项
   */
  constructor(options: PreviewRendererOptions) {
    this.options = options;
    
    // 创建场景
    this.scene = new THREE.Scene();
    
    // 创建相机
    this.camera = new THREE.PerspectiveCamera(45, options.width / options.height, 0.1, 1000);
    (this.camera as any).setPosition(0, 10, 10);
    this.camera.lookAt(0, 0, 0);
    
    // 创建渲染器
    this.renderer = new THREE.WebGLRenderer({ antialias: true });
    this.renderer.setSize(options.width, options.height);
    this.renderer.setClearColor(options.backgroundColor || 0x000000);
    
    // 创建光源
    this.ambientLight = new THREE.AmbientLight(0x404040);
    this.scene.add(this.ambientLight);
    
    this.directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    (this.directionalLight as any).setPosition(1, 1, 1);
    this.scene.add(this.directionalLight);
    
    // 如果需要阴影
    if (options.useShadows) {
      this.renderer.shadowMap.enabled = true;
      this.directionalLight.castShadow = true;
    }
    
    // 如果需要网格
    if (options.useGrid) {
      this.gridHelper = new THREE.GridHelper(10, 10);
      this.scene.add(this.gridHelper);
    }
    
    this.initialized = true;
  }
  
  /**
   * 获取渲染器DOM元素
   * @returns 渲染器DOM元素
   */
  public getDomElement(): HTMLCanvasElement {
    return this.renderer.domElement;
  }
  
  /**
   * 预览高度图
   * @param heightMap 高度图（URL、File、Blob、ImageData、Canvas、Image）
   * @param options 导入选项
   * @returns Promise，解析为是否预览成功
   */
  public async previewHeightMap(
    heightMap: string | File | Blob | ImageData | HTMLCanvasElement | HTMLImageElement,
    options: HeightMapImportOptions = {}
  ): Promise<boolean> {
    try {
      // 创建临时地形组件
      const terrain = new TerrainComponent();
      
      // 导入高度图
      const success = await terrain.importFromHeightMap(heightMap, options);
      if (!success) {
        return false;
      }
      
      // 创建预览
      this.createPreviewMesh(terrain);
      
      // 渲染
      this.render();
      
      return true;
    } catch (error) {
      Debug.error('TerrainPreviewService', '预览高度图失败:', error);
      return false;
    }
  }
  
  /**
   * 预览JSON
   * @param json JSON字符串
   * @param options 导入选项
   * @returns 是否预览成功
   */
  public previewJSON(
    json: string,
    options: TerrainImportOptions = {}
  ): boolean {
    try {
      // 创建临时地形组件
      const terrain = new TerrainComponent();
      
      // 导入JSON
      const success = terrain.importFromJSON(json, options);
      if (!success) {
        return false;
      }
      
      // 创建预览
      this.createPreviewMesh(terrain);
      
      // 渲染
      this.render();
      
      return true;
    } catch (error) {
      Debug.error('TerrainPreviewService', '预览JSON失败:', error);
      return false;
    }
  }
  
  /**
   * 预览第三方格式
   * @param format 格式
   * @param data 地形数据
   * @param options 导入选项
   * @returns Promise，解析为是否预览成功
   */
  public async previewThirdPartyFormat(
    format: ThirdPartyTerrainFormat,
    data: ArrayBuffer | string,
    options: Omit<ThirdPartyTerrainImportOptions, 'format'> = {}
  ): Promise<boolean> {
    try {
      // 创建临时地形组件
      const terrain = new TerrainComponent();
      
      // 导入第三方格式
      const success = await terrain.importFromThirdPartyFormat(format, data, options);
      if (!success) {
        return false;
      }
      
      // 创建预览
      this.createPreviewMesh(terrain);
      
      // 渲染
      this.render();
      
      return true;
    } catch (error) {
      Debug.error('TerrainPreviewService', '预览第三方格式失败:', error);
      return false;
    }
  }
  
  /**
   * 创建预览网格
   * @param terrain 地形组件
   */
  private createPreviewMesh(terrain: TerrainComponent): void {
    // 清除现有网格
    if (this.terrainMesh) {
      this.scene.remove(this.terrainMesh);
      this.terrainMesh = null;
    }
    
    // 清除现有几何体
    if (this.terrainGeometry) {
      (this.terrainGeometry as any).dispose();
      this.terrainGeometry = null;
    }
    
    // 清除现有材质
    if (this.terrainMaterial) {
      if (Array.isArray(this.terrainMaterial)) {
        this.terrainMaterial.forEach(material => (material as any).dispose());
      } else {
        (this.terrainMaterial as any).dispose();
      }
      this.terrainMaterial = null;
    }
    
    // 创建地形几何体
    const geometry = new THREE.PlaneGeometry(
      10,
      10,
      terrain.resolution - 1,
      terrain.resolution - 1
    );
    
    // 设置顶点高度
    const positions = geometry.attributes.position.array;
    for (let i = 0; i < positions.length / 3; i++) {
      const x = i % terrain.resolution;
      const z = Math.floor(i / terrain.resolution);
      const index = z * terrain.resolution + x;
      
      // 设置高度
      positions[i * 3 + 1] = terrain.heightData[index] * 5;
    }
    
    // 更新几何体
    geometry.computeVertexNormals();
    
    // 创建材质
    const material = new THREE.MeshStandardMaterial({
      color: 0x3d8c40,
      side: THREE.DoubleSide,
      flatShading: false,
      metalness: 0.0,
      roughness: 0.8,
      wireframe: this.options.useWireframe || false
    });
    
    // 创建网格
    const mesh = new THREE.Mesh(geometry, material);
    mesh.rotation.x = -Math.PI / 2;
    
    // 添加到场景
    this.scene.add(mesh);
    
    // 保存引用
    this.terrainMesh = mesh;
    this.terrainGeometry = geometry;
    this.terrainMaterial = material;
    
    // 调整相机位置
    (this.camera as any).setPosition(0, 10, 10);
    this.camera.lookAt(0, 0, 0);
  }
  
  /**
   * 渲染
   */
  public render(): void {
    if (!this.initialized) {
      return;
    }
    
    this.renderer.render(this.scene, this.camera);
  }
  
  /**
   * 调整大小
   * @param width 宽度
   * @param height 高度
   */
  public resize(width: number, height: number): void {
    this.options.width = width;
    this.options.height = height;
    
    this.camera.aspect = width / height;
    this.camera.updateProjectionMatrix();
    
    this.renderer.setSize(width, height);
    this.render();
  }
  
  /**
   * 销毁
   */
  public dispose(): void {
    // 清除网格
    if (this.terrainMesh) {
      this.scene.remove(this.terrainMesh);
      this.terrainMesh = null;
    }
    
    // 清除几何体
    if (this.terrainGeometry) {
      (this.terrainGeometry as any).dispose();
      this.terrainGeometry = null;
    }
    
    // 清除材质
    if (this.terrainMaterial) {
      if (Array.isArray(this.terrainMaterial)) {
        this.terrainMaterial.forEach(material => (material as any).dispose());
      } else {
        (this.terrainMaterial as any).dispose();
      }
      this.terrainMaterial = null;
    }
    
    // 清除渲染器
    (this.renderer as any).dispose();
    
    this.initialized = false;
  }
}
